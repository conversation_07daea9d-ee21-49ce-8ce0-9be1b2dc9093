#!/usr/bin/env node

/**
 * Pre-commit Hook 脚本
 * 在提交前执行 TypeScript 类型检查和 ESLint 代码规范检查
 */

import { execSync } from 'child_process'
import { existsSync } from 'fs'
import path from 'path'
import process from 'process'

// 配置
const config = {
  enableTypeCheck: true,
  enableLint: true,
  strict: true,
  packages: {
    main: {
      enabled: true,
      typeCheckCommand: 'npm run typecheck',
      path: 'packages/main'
    },
    renderer: {
      enabled: true,
      typeCheckCommand: 'npm run typecheck',
      path: 'packages/renderer'
    }
  },
  globalLintCommand: 'npm run lint'
}

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
}

function log(message, color = colors.reset) {
  // eslint-disable-next-line no-undef
  console.log(`${color}${message}${colors.reset}`)
}

function logErrorDetails(title, errorOutput, color = colors.red) {
  log(`\n📋 ${title}:`, colors.yellow)
  log('─'.repeat(60), colors.yellow)

  // 清理和格式化错误输出
  const cleanOutput = errorOutput
    .replace(/\x1b\[[0-9;]*m/g, '') // 移除 ANSI 颜色代码
    .trim()

  if (cleanOutput) {
    log(cleanOutput, color)
  } else {
    log('未能获取详细错误信息', colors.yellow)
  }

  log('─'.repeat(60), colors.yellow)
}

function execCommand(command, cwd = process.cwd(), silent = false) {
  try {
    const result = execSync(command, {
      cwd,
      encoding: 'utf8',
      stdio: silent ? 'pipe' : 'inherit'
    })
    return { success: true, output: result }
  } catch (error) {
    // 捕获详细的错误输出
    const errorOutput = error.stdout || error.stderr || error.message || '未知错误'
    return {
      success: false,
      error,
      errorOutput: errorOutput.toString(),
      command,
      cwd
    }
  }
}

async function runPreCommitChecks() {
  log('🔍 执行 pre-commit 检查...', colors.cyan)

  let errorCount = 0
  const errors = []

  // 1. TypeScript 类型检查
  if (config.enableTypeCheck) {
    for (const [packageName, packageConfig] of Object.entries(config.packages)) {
      if (!packageConfig.enabled) continue

      log(`\n📝 检查 ${packageName} TypeScript 类型...`, colors.blue)

      const packagePath = path.resolve(packageConfig.path)
      if (!existsSync(packagePath)) {
        log(`⚠️  包目录不存在: ${packagePath}`, colors.yellow)
        continue
      }

      const result = execCommand(packageConfig.typeCheckCommand, packagePath, true)

      if (result.success) {
        log(`✅ ${packageName} TypeScript 类型检查通过`, colors.green)
      } else {
        log(`❌ ${packageName} TypeScript 类型检查失败`, colors.red)
        logErrorDetails(`${packageName} TypeScript 错误详情`, result.errorOutput)
        errorCount++
        errors.push(`${packageName} TypeScript 类型检查`)
      }
    }
  }

  // 2. ESLint 代码规范检查
  // if (config.enableLint) {
  //   log('\n🔧 执行 ESLint 代码规范检查...', colors.blue)
  //
  //   const result = execCommand(config.globalLintCommand, process.cwd(), true)
  //
  //   if (result.success) {
  //     log('✅ ESLint 代码规范检查通过', colors.green)
  //   } else {
  //     log('❌ ESLint 代码规范检查失败', colors.red)
  //     logErrorDetails('ESLint 错误详情', result.errorOutput)
  //     log('\n💡 提示: 运行 \'npm run lint:fix\' 尝试自动修复部分问题', colors.cyan)
  //     errorCount++
  //     errors.push('ESLint 代码规范检查')
  //   }
  // }

  // 输出结果
  log('\n==========================================', colors.cyan)

  if (errorCount === 0) {
    log('✅ 所有检查通过，允许提交', colors.green)
    process.exit(0)
  } else {
    log(`❌ 发现 ${errorCount} 个检查失败`, colors.red)
    log('\n失败的检查:', colors.yellow)
    errors.forEach(error => log(`  - ${error}`, colors.red))

    log('\n请修复以上问题后再次提交。', colors.yellow)
    log('\n💡 常用修复命令:', colors.cyan)
    log('  npm run lint:fix           # 自动修复 ESLint 问题', colors.blue)
    log('  npm run pre-commit:fix     # 运行修复和检查', colors.blue)

    log('\n如果你确定要跳过这些检查，可以使用:', colors.cyan)
    log('  git commit --no-verify', colors.blue)
    log('==========================================', colors.cyan)

    if (config.strict) {
      process.exit(1)
    } else {
      log('\n⚠️  警告模式：检查失败但允许提交', colors.yellow)
      process.exit(0)
    }
  }
}

// 运行检查
runPreCommitChecks().catch(error => {
  log(`❌ Pre-commit 检查过程中发生错误: ${error.message}`, colors.red)
  process.exit(1)
})
