import pkg from './package.json' with {type: 'json'}
import mapWorkspaces from '@npmcli/map-workspaces'
import {join} from 'node:path'
import {pathToFileURL} from 'node:url'
import process from 'node:process'
import * as dotenv from 'dotenv'

dotenv.config()

// 获取当前的发布渠道
function getDistributionChannel() {
  if (process.env.VITE_DISTRIBUTION_CHANNEL) {
    return process.env.VITE_DISTRIBUTION_CHANNEL
  }

  throw new Error('VITE_DISTRIBUTION_CHANNEL not set')
}

// 获取平台特定的发布配置
function getPlatformPublishConfig() {
  const channel = getDistributionChannel()
  const baseUrl = 'https://dev-common-**********.cos.ap-guangzhou.myqcloud.com'

  return [
    {
      provider: 'generic',
      url: `${baseUrl}/clipnest-update/${channel}/latest/`,
      channel: channel
    }
  ]
}

const appShortId = 'hican-dev'
const copyright = '© 2025 gaoling.tech'
const productNameCn = '超级罐头(内部开发版)'

export default /** @type import('electron-builder').Configuration */
({
  appId: `com.gaolingtech.${appShortId}`,
  productName: productNameCn,
  executableName: appShortId, // exe 文件名保持英文
  copyright,
  buildVersion: pkg.version, // 使用完整版本号包括构建元数据
  directories: {
    output: 'dist',
    buildResources: 'buildResources',
  },
  generateUpdatesFilesForAllChannels: false,
  extraResources: [
    {
      from: 'migrations',
      to: 'migrations',
      filter: ['*']
    },
  ],
  artifactName: `${appShortId}-${pkg.version}-\${os}-\${arch}.\${ext}`,
  publish: getPlatformPublishConfig(),
  files: [
    'LICENSE*',
    pkg.main,
    '!node_modules/@app/**',
    ...await getListOfFilesFromEachWorkspace(),
  ],
  win: {
    target: [
      {
        target: 'nsis',
        arch: ['x64']
      }
    ],
    verifyUpdateCodeSignature: false,
    legalTrademarks: `Copyright ${copyright}`,
    requestedExecutionLevel: 'asInvoker',
    extraResources: [
      {
        from: 'resources/ffmpeg.exe',
        to: 'ffmpeg.exe'
      },
      {
        from: 'resources/ffprobe.exe',
        to: 'ffprobe.exe'
      }
    ]
  },
  mac: {
    target: [
      {
        target: 'dmg',
        arch: ['x64', 'arm64']
      }
    ],
    category: 'public.app-category.productivity',
    icon: 'buildResources/icon.icns',
    darkModeSupport: true,
    hardenedRuntime: true,
    gatekeeperAssess: false,
    entitlements: 'buildResources/entitlements.mac.plist',
    entitlementsInherit: 'buildResources/entitlements.mac.plist',
    extraResources: [
      {
        from: 'resources/ffmpeg',
        to: 'ffmpeg'
      },
      {
        from: 'resources/ffprobe',
        to: 'ffprobe'
      }
    ]
  },
  dmg: {
    title: '${productName} ${version}',
    icon: 'buildResources/icon.icns',
    background: 'buildResources/dmg-background.png',
    contents: [
      {
        x: 130,
        y: 220
      },
      {
        x: 410,
        y: 220,
        type: 'link',
        path: '/Applications'
      }
    ],
    window: {
      width: 540,
      height: 380
    }
  },
  nsis: {
    shortcutName: productNameCn,
    oneClick: false,
    allowToChangeInstallationDirectory: true,
    allowElevation: true,
    createDesktopShortcut: 'always',
    createStartMenuShortcut: false,
    menuCategory: false,
    runAfterFinish: false,
    include: "buildResources/installer.nsh",
    uninstallDisplayName: productNameCn, // 卸载程序显示中文名称
    // 设置默认安装目录为 Program Files
    perMachine: true
  }
})

/**
 * By default, electron-builder copies each package into the output compilation entirety,
 * including the source code, tests, configuration, assets, and any other files.
 *
 * So you may get compiled app structure like this:
 * ```
 * app/
 * ├── node_modules/
 * │   └── workspace-packages/
 * │       ├── package-a/
 * │       │   ├── src/            # Garbage. May be safely removed
 * │       │   ├── dist/
 * │       │   │   └── index.js    # Runtime code
 * │       │   ├── vite.config.js  # Garbage
 * │       │   ├── .env            # some sensitive config
 * │       │   └── package.json
 * │       ├── package-b/
 * │       ├── package-c/
 * │       └── package-d/
 * ├── packages/
 * │   └── entry-point.js
 * └── package.json
 * ```
 *
 * To prevent this, we read the "files"
 * property from each package's package.json
 * and add all files that do not match the patterns to the exclusion list.
 *
 * This way,
 * each package independently determines which files will be included in the final compilation and which will not.
 *
 * So if `package-a` in its `package.json` describes
 * ```json
 * {
 *   "name": "package-a",
 *   "files": [
 *     "dist/**\/"
 *   ]
 * }
 * ```
 *
 * Then in the compilation only those files and `package.json` will be included:
 * ```
 * app/
 * ├── node_modules/
 * │   └── workspace-packages/
 * │       ├── package-a/
 * │       │   ├── dist/
 * │       │   │   └── index.js    # Runtime code
 * │       │   └── package.json
 * │       ├── package-b/
 * │       ├── package-c/
 * │       └── package-d/
 * ├── packages/
 * │   └── entry-point.js
 * └── package.json
 * ```
 */
async function getListOfFilesFromEachWorkspace() {

  /**
   * @type {Map<string, string>}
   */
  const workspaces = await mapWorkspaces({
    cwd: process.cwd(),
    pkg,
  })

  const allFilesToInclude = []

  for (const [name, path] of workspaces) {
    // 跳过 workers 包，因为不再需要独立打包
    if (name === '@app/workers') {
      continue
    }

    const pkgPath = join(path, 'package.json')
    const { default: workspacePkg } = await import(pathToFileURL(pkgPath), { with: { type: 'json' } })

    let patterns = workspacePkg.files || ['dist/**', 'package.json']

    patterns = patterns.map(p => join('node_modules', name, p))
    allFilesToInclude.push(...patterns)
  }

  return allFilesToInclude
}
