
-- 创建渲染任务表
CREATE TABLE IF NOT EXISTS render_task (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  team_id INTEGER NOT NULL DEFAULT 0,
  uid TEXT NOT NULL DEFAULT '',
  task_no TEXT NOT NULL DEFAULT '',
  name TEXT NOT NULL DEFAULT '',
  cover_url TEXT NOT NULL DEFAULT '',
  cover_object_id TEXT NOT NULL DEFAULT '',
  script_id INTEGER NOT NULL DEFAULT 0,
  object_id TEXT NOT NULL DEFAULT '',
  resolution TEXT NOT NULL DEFAULT '',
  fps INTEGER NOT NULL DEFAULT 30,
  duration REAL NOT NULL DEFAULT 0,
  progress REAL NOT NULL DEFAULT 0,
  status INTEGER NOT NULL DEFAULT 0,
  reason TEXT NOT NULL DEFAULT '',
  file_size INTEGER NOT NULL DEFAULT 0,
  download_url TEXT NOT NULL DEFAULT '',
  render_start_time INTEGER NOT NULL DEFAULT 0,
  render_end_time INTEGER NOT NULL DEFAULT 0,
  deleted_at INTEGER NOT NULL DEFAULT 0,
  updated_at INTEGER NOT NULL DEFAULT 0,
  created_at INTEGER NOT NULL DEFAULT 0
);

CREATE INDEX IF NOT EXISTS idx_render_task_uid ON render_task(uid);
CREATE INDEX IF NOT EXISTS idx_render_task_team_id ON render_task(team_id);
CREATE INDEX IF NOT EXISTS idx_render_task_status ON render_task(status);
CREATE INDEX IF NOT EXISTS idx_render_task_task_no ON render_task(task_no);
CREATE INDEX IF NOT EXISTS idx_render_task_script_id ON render_task(script_id);
CREATE INDEX IF NOT EXISTS idx_render_task_deleted_at ON render_task(deleted_at);
CREATE INDEX IF NOT EXISTS idx_render_task_created_at ON render_task(created_at);
