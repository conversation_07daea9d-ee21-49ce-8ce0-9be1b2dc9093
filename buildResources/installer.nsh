!include "LogicLib.nsh"

!define APP_NAME "hican-dev"

!macro preInit
  StrCpy $INSTDIR "C:\Program Files (x86)\${APP_NAME}"
!macroend

; 验证和调整安装目录的函数
Function .onVerifyInstDir
  Push $0  ; 用于存储临时字符串
  Push $1  ; 用于存储路径长度
  Push $2  ; 用于存储应用名称长度
  Push $3  ; 用于存储路径末尾部分

  ; 检查安装目录是否为空
  ${If} $INSTDIR == ""
    ; 如果为空，设置默认目录
    StrCpy $INSTDIR "C:\Program Files (x86)\${APP_NAME}"
    Goto done
  ${EndIf}

  ; 获取应用名称长度
  StrLen $2 "${APP_NAME}"

  ; 获取安装路径长度
  StrLen $1 $INSTDIR

  ; 检查路径是否足够长以包含应用名称
  ${If} $1 < $2
    ; 路径太短，直接添加应用名称
    StrCpy $INSTDIR "$INSTDIR\${APP_NAME}"
    Goto done
  ${EndIf}

  ; 提取路径末尾与应用名称长度相等的部分
  IntOp $0 $1 - $2
  StrCpy $3 $INSTDIR "" $0

  ; 检查路径是否已经以应用名称结尾
  ${If} $3 != "${APP_NAME}"
    ; 检查路径是否以反斜杠结尾
    StrCpy $0 $INSTDIR "" -1
    ${If} $0 == "\"
      ; 路径以反斜杠结尾，直接添加应用名称
      StrCpy $INSTDIR "$INSTDIR${APP_NAME}"
    ${Else}
      ; 路径不以反斜杠结尾，添加反斜杠和应用名称
      StrCpy $INSTDIR "$INSTDIR\${APP_NAME}"
    ${EndIf}
  ${EndIf}

  done:
  Pop $3
  Pop $2
  Pop $1
  Pop $0
FunctionEnd
