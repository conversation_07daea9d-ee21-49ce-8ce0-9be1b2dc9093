import { animationTemplates } from '@clipnest/remotion-shared/constants'
import { useCurrentFrame } from 'remotion'
import { useMemo } from 'react'
import { useRenderContext } from '../render.context'
import { Overlay } from '@app/shared/types/overlay'

export const useOverlayAnimation = (overlay: Overlay) => {
  const { playerMetadata: { fps } } = useRenderContext()
  const frame = useCurrentFrame()

  const isExitPhase = frame >= overlay.durationInFrames - fps

  const animation = useMemo(() => {
    if (!('styles' in overlay) || !overlay.styles) return null
    if (!('animation' in overlay.styles)) return null

    return overlay.styles.animation || null
  }, [overlay])

  const enterAnimation = !isExitPhase && animation?.enter
    ? animationTemplates[animation.enter]?.enter(
      frame,
      overlay.durationInFrames,
    )
    : {}

  const exitAnimation = isExitPhase && animation?.exit
    ? animationTemplates[animation.exit]?.exit(
      frame,
      overlay.durationInFrames,
    )
    : {}

  return isExitPhase ? exitAnimation : enterAnimation
}
