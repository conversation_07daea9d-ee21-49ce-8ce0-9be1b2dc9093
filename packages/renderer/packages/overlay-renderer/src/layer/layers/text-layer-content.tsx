import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { TextOverlay } from '@app/shared/types/overlay'
import { useOverlayAnimation } from '../../hooks'
import { calculateTextRenderInfo, TextRenderInfo } from '../../utils/calculateTextRenderInfo'
import { continueRender, delayRender } from 'remotion'
import * as opentype from 'opentype.js'
import { TextToSvgConvertor } from '../../utils/text-to-svg'

interface TextLayerContentProps {
  overlay: TextOverlay
}

const ADDITIONAL_BOX_HEIGHT = 0

const fontCache = new Map<string, opentype.Font>()

interface CloudTextRendererProps {
  overlay: TextOverlay
  containerStyle?: React.CSSProperties
}

async function loadOpentypeFont(src: string) {
  const cache = fontCache.get(src)
  if (cache) return cache

  const font = await opentype.load(src)
  fontCache.set(src, font)
  return font
}

const TextRenderSvgPart: React.FC<{
  overlay: TextOverlay, font: opentype.Font, renderInfo: TextRenderInfo
}> = ({ overlay, font, renderInfo }) => {
  const { styles: textStyles } = overlay

  if (!textStyles.strokeEnabled && !textStyles.shadowEnabled) return null

  const hasStroke = Boolean(
    textStyles.strokeEnabled
    && textStyles.strokeWidth
    && textStyles.strokeColor
  )

  const svgRef = useRef<SVGSVGElement>(null)

  // 确保有有效的尺寸
  if (overlay.width === 0) {
    return null
  }

  const filterId = useMemo(() => overlay.styles.shadowEnabled ? `shadow-${overlay.id}` : null, [overlay])

  const filters = useMemo(
    () => {
      const filters: React.ReactElement[] = []

      if (filterId) {
        const distance = overlay.styles.shadowDistance
          ? overlay.styles.shadowDistance / 100 * overlay.styles.fontSize
          : 0

        const angle = overlay.styles.shadowAngle ?? 0
        const blur = overlay.styles.shadowBlur ?? 0
        const color = overlay.styles.shadowColor || '#000000'
        const opacity = overlay.styles.shadowOpacity ?? 0

        if (distance > 0) {
          const angleRad = (angle * Math.PI) / 180
          const offsetX = Math.cos(angleRad) * distance
          const offsetY = Math.sin(angleRad) * distance

          filters.push(
            <filter
              key="shadow"
              id={filterId}
              x="-100%"
              y="-100%"
              width="400%"
              height="400%"
              filterUnits="userSpaceOnUse"
              colorInterpolationFilters="sRGB"
            >
              <feDropShadow
                dx={offsetX}
                dy={offsetY}
                stdDeviation={blur}
                floodColor={color}
                floodOpacity={opacity}
              />
            </filter>
          )
        }
      }

      return filters
    },
    [textStyles, filterId]
  )

  const paths = useMemo(
    () => {
      try {
        const strokeWidth = (textStyles.strokeWidth ?? 0)
          / 100
          * textStyles.fontSize
          * 0.3

        const strokeColor = textStyles.strokeColor || '#000000'
        const textToSvg = new TextToSvgConvertor(font, font)
        const paths: React.ReactElement[] = []

        const {
          x, y, fontSize, wrappedLines, lineHeight, lineSpacing
        } = renderInfo

        // 为每一行生成 SVG 路径
        wrappedLines.forEach((lineInfo, index) => {
          if (!lineInfo.content.trim()) return // 跳过空行

          const { d } = textToSvg.getD(lineInfo.content, {
            x: 0,
            y: 0,
            fontSize: fontSize,
            anchor: 'left middle',
            letterSpacing: (textStyles.letterSpacing ?? 0) / fontSize // 转换为相对值
          })

          if (!d) return

          // 计算每行的位置：基础位置 + 行索引 * (行高 + 行间距)
          const lineY = y + (index * (lineHeight + lineSpacing))

          // 根据 textAlign 计算水平位置
          const textAlign = textStyles.textAlign || 'center'
          const lineWidth = lineInfo.width // 直接使用预计算的宽度

          let lineX: number
          switch (textAlign) {
            case 'left':
              lineX = x
              break
            case 'right':
              lineX = x - lineWidth
              break
            case 'center':
            default:
              lineX = x - lineWidth / 2
              break
          }

          // 构建变换字符串，包含位置和样式变换
          let transform = `translate(${lineX}, ${lineY})`

          // 添加斜体变换
          if (textStyles.fontStyle === 'italic') {
            transform += ' skewX(-12)' // 向左倾斜 12 度来模拟斜体
          }

          const basePathProps: any = {
            d,
            fill: 'none',
            stroke: hasStroke ? strokeColor : 'transparent',
            strokeWidth,
            strokeLinejoin: 'round',
            strokeLinecap: 'round',
            transform,
            style: {
              vectorEffect: 'non-scaling-stroke'
            }
          }

          // 渲染主要文本路径
          paths.push(
            <path
              key={`line-${index}`}
              {...basePathProps}
            />
          )
          if (filterId) {
            paths.push(
              <path
                key={`line-${index}-shadow`}
                {...basePathProps}
                fill={textStyles.shadowColor}
                // stroke="transparent"
                strokeWidth={strokeWidth}
                filter={`url(#${filterId})`}
              />
            )
          }

          // 如果是加粗，添加额外的路径来增加厚度
          if (textStyles.fontWeight === 'bold') {
            const boldOffset = Math.max(0.5, fontSize * 0.01)

            // 添加多个偏移的路径来模拟加粗效果
            const boldTransforms = [
              `translate(${lineX + boldOffset}, ${lineY})`,
              `translate(${lineX}, ${lineY + boldOffset})`,
              `translate(${lineX + boldOffset}, ${lineY + boldOffset})`
            ]

            boldTransforms.forEach((boldTransform, boldIndex) => {
              let finalBoldTransform = boldTransform
              if (textStyles.fontStyle === 'italic') {
                finalBoldTransform += ' skewX(-12)'
              }

              paths.push(
                <path
                  key={`line-bold-${index}-${boldIndex}`}
                  d={d}
                  fill="none"
                  stroke={hasStroke ? strokeColor : 'none'}
                  strokeWidth={strokeWidth}
                  strokeLinejoin="round"
                  strokeLinecap="round"
                  // filter={filterId ? `url(#${filterId})` : undefined}
                  transform={finalBoldTransform}
                  style={{
                    vectorEffect: 'non-scaling-stroke'
                  }}
                />
              )
            })
          }

          // 添加下划线
          if (textStyles.underlineEnabled) {
          // 由于 SVG anchor 是 'left middle'，lineY 是文字中心
          // 下划线应该在文字底部，所以需要加上字体大小的一半，再稍微向下偏移
            const underlineY = lineY + fontSize * 0.5 + fontSize * 0.1
            const underlineThickness = Math.max(1, fontSize * 0.05) // 下划线厚度

            paths.push(
              <line
                key={`underline-${index}`}
                x1={lineX}
                y1={underlineY}
                x2={lineX + lineWidth}
                y2={underlineY}
                stroke={hasStroke ? strokeColor : textStyles.color || '#ffffff'}
                strokeWidth={underlineThickness}
                strokeLinecap="round"
              // filter={filterId ? `url(#${filterId})` : undefined}
              />
            )
          }
        })

        return <g>{paths}</g>
      } catch (error) {
        console.error('[增强文本渲染器] SVG路径渲染失败:', error)
        return null
      }
    },
    [overlay, font, hasStroke, textStyles, renderInfo, filterId]
  )

  // 计算 SVG 需要的高度
  const requiredHeight = Math.max(overlay.height, renderInfo.totalHeight + ADDITIONAL_BOX_HEIGHT)

  return (
    <svg
      ref={svgRef}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        overflow: 'visible',
        zIndex: 1,
        pointerEvents: 'none'
      }}
      viewBox={`0 0 ${overlay.width} ${requiredHeight}`}
      preserveAspectRatio="xMidYMid meet"
    >
      <defs>
        {filters}
      </defs>
      {paths}
    </svg>
  )
}

const TextRendererCanvasPart: React.FC<{
  overlay: TextOverlay, renderInfo: TextRenderInfo
}> = ({ overlay, renderInfo }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)

  /**
   * 渲染带字间距的文本行
   * @param ctx Canvas 2D 上下文
   * @param text 要渲染的文本
   * @param x 起始 X 坐标
   * @param y Y 坐标
   * @param letterSpacing 字间距
   * @param textAlign 文本对齐方式
   * @param lineWidth 行的总宽度（用于对齐计算）
   */
  const renderTextWithLetterSpacing = useCallback((
    ctx: CanvasRenderingContext2D,
    text: string,
    x: number,
    y: number,
    letterSpacing: number,
    textAlign: string,
    lineWidth: number
  ) => {
    // 计算起始位置，考虑文本对齐
    let startX = x
    if (textAlign === 'center') {
      startX = x - lineWidth / 2
    } else if (textAlign === 'right') {
      startX = x - lineWidth
    }

    if (letterSpacing === 0) {
      // 如果没有字间距，使用原生渲染，但仍需考虑对齐
      ctx.fillText(text, startX, y)
      return
    }

    // 逐字符渲染
    let currentX = startX
    for (let i = 0; i < text.length; i++) {
      const char = text[i]
      ctx.fillText(char, currentX, y)

      // 计算字符宽度并添加字间距
      const charWidth = ctx.measureText(char).width
      currentX += charWidth

      // 在字符之间添加字间距（最后一个字符后不加）
      if (i < text.length - 1) {
        currentX += letterSpacing
      }
    }
  }, [])

  // Canvas 渲染效果（支持多行文本）
  useEffect(
    () => {
      if (!canvasRef.current || overlay.width === 0) {
        return
      }

      const { styles: textStyle } = overlay
      const canvas = canvasRef.current
      if (!canvas) return

      const ctx = canvas.getContext('2d')
      if (!ctx) return

      const {
        x, y, totalHeight, wrappedLines, lineHeight, letterSpacing, lineSpacing
      } = renderInfo

      // 动态调整 Canvas 高度以适应文本内容
      const requiredHeight = Math.max(overlay.height, totalHeight + ADDITIONAL_BOX_HEIGHT) // 添加一些padding

      // 设置 Canvas 尺寸
      canvas.width = overlay.width * window.devicePixelRatio
      canvas.height = requiredHeight * window.devicePixelRatio
      canvas.style.width = `${overlay.width}px`
      canvas.style.height = `${requiredHeight}px`

      // 缩放上下文以适应高DPI显示
      ctx.scale(window.devicePixelRatio, window.devicePixelRatio)

      // 清除画布
      ctx.clearRect(0, 0, overlay.width, requiredHeight)

      // 构建字体字符串（不包含 fontWeight 和 fontStyle，这两个属性将由其他方式实现）
      const fallbackFontFamily = textStyle.fontFamily || 'Arial'

      // 设置文字样式
      ctx.font = `${overlay.styles.fontSize}px "${overlay.styles.fontFamily}", ${fallbackFontFamily}, sans-serif`
      ctx.fillStyle = overlay.styles.color || '#ffffff'
      ctx.textBaseline = 'middle'
      // 注意：不设置 ctx.textAlign，因为我们会在 renderTextWithLetterSpacing 中手动处理对齐

      // 逐行渲染文本
      wrappedLines.forEach((lineInfo, index) => {
        // 计算行位置：基础位置 + 行索引 * (行高 + 行间距)
        const lineY = y + (index * (lineHeight + lineSpacing))

        ctx.save()

        // 计算斜体偏移补偿
        let italicOffsetX = 0
        if (textStyle.fontStyle === 'italic') {
          // 计算斜体变换导致的水平偏移
          // skew 变换会导致文本在 Y 轴方向上的位置影响 X 轴位置
          const skewFactor = -0.2
          italicOffsetX = lineY * skewFactor

          // 应用斜体变换
          ctx.transform(1, 0, skewFactor, 1, 0, 0)
        }

        // 计算补偿后的 X 坐标
        const adjustedX = x - italicOffsetX

        // 使用支持字间距的渲染函数
        const textAlign = textStyle.textAlign || 'center'
        renderTextWithLetterSpacing(
          ctx,
          lineInfo.content,
          adjustedX,
          lineY,
          letterSpacing,
          textAlign,
          lineInfo.width
        )

        // 如果是加粗，通过多次渲染来模拟加粗效果
        if (textStyle.fontWeight === 'bold') {
          const boldOffset = Math.max(0.5, overlay.styles.fontSize * 0.01)

          // 加粗效果也需要使用字间距渲染
          renderTextWithLetterSpacing(
            ctx,
            lineInfo.content,
            adjustedX + boldOffset,
            lineY,
            letterSpacing,
            textAlign,
            lineInfo.width
          )
          renderTextWithLetterSpacing(
            ctx,
            lineInfo.content,
            adjustedX,
            lineY + boldOffset,
            letterSpacing,
            textAlign,
            lineInfo.width
          )
          renderTextWithLetterSpacing(
            ctx,
            lineInfo.content,
            adjustedX + boldOffset,
            lineY + boldOffset,
            letterSpacing,
            textAlign,
            lineInfo.width
          )
        }

        ctx.restore()
      })
    },
    [overlay, renderInfo, renderTextWithLetterSpacing]
  )

  return (
    <canvas
      ref={canvasRef}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        zIndex: 2,
        pointerEvents: 'none'
      }}
    />
  )
}

const OpentypeFontBasedTextRenderer: React.FC<{
  font: opentype.Font,
  overlay: TextOverlay,
  containerStyle: any
}> = ({ font, overlay, containerStyle }) => {
  const { styles: textStyle } = overlay

  const hasBubbleBackground = !!textStyle.backgroundImage
  const shouldUseBubblePosition = hasBubbleBackground && !!overlay.styles.bubbleTextRect

  const textRenderInfo = useMemo(
    () => calculateTextRenderInfo(font, overlay),
    [font, overlay]
  )

  // 增强容器样式，确保背景图片正确显示
  const finalContainerStyle: React.CSSProperties = {
    ...containerStyle,
    backgroundImage: hasBubbleBackground ? `url(${overlay.styles.backgroundImage})` : undefined,
    backgroundSize: 'contain',
    backgroundRepeat: 'no-repeat',
    backgroundPosition: 'center'
  }

  // 计算气泡图中文字的位置和样式
  const bubbleTextStyle = useMemo((): React.CSSProperties => {
    if (!hasBubbleBackground) {
      return {}
    }

    return {
      position: 'absolute',
      left: '50%',
      top: '50%',
      transform: 'translate(-50%, -50%)',
      width: '100%',
      height: '60%',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      boxSizing: 'border-box'
    }
  }, [hasBubbleBackground])

  const renderDualLayerContent = useCallback(() => (
    <div
      style={{
        position: 'relative',
        width: '100%',
        height: overlay.height,
        backgroundColor: textStyle.backgroundColor,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        opacity: overlay.styles.textOpacity ?? 1,
        overflow: 'visible',
      }}
    >
      {/* SVG 层 - 下层，渲染描边和阴影效果 */}
      <TextRenderSvgPart overlay={overlay} renderInfo={textRenderInfo} font={font} />

      {/* Canvas 层 - 上层，负责文字填充 */}
      <TextRendererCanvasPart overlay={overlay} renderInfo={textRenderInfo} />
    </div>
  ), [overlay, textRenderInfo, font])

  const renderMainContent = () => {
    if (shouldUseBubblePosition) {
      return (
        <div style={bubbleTextStyle}>
          {renderDualLayerContent()}
        </div>
      )
    }

    return renderDualLayerContent()
  }

  const finalHeight = Math.max(overlay.height, textRenderInfo.totalHeight + ADDITIONAL_BOX_HEIGHT)

  return (
    <div
      style={{
        ...finalContainerStyle,
        position: 'relative',
        overflow: 'visible',
        height: `${finalHeight}px`
      }}
    >
      {renderMainContent()}
    </div>
  )
}

const TextLayerRendererImpl: React.FC<CloudTextRendererProps> = ({
  overlay,
  containerStyle = { width: '100%', height: '100%' }
}) => {
  const [handle] = useState(() => delayRender())

  const [font, setFont] = useState<opentype.Font>()

  const loadFont = async (fontSrc: string, fontLocalSrc?: string, fontFamily?: string) => {
    if (!fontSrc) {
      return null
    }

    const font = await loadOpentypeFont(fontLocalSrc || fontSrc)

    try {
      if (fontFamily) {
        const existingFontFace = Array.from(document.fonts).find(
          face => face.family === fontFamily
        )

        if (!existingFontFace) {
          const fontFace = new FontFace(fontFamily, `url("${fontSrc}")`)
          await fontFace.load()
          document.fonts.add(fontFace)
        }
      }
    } catch (domError) {
      console.warn('[CLOUD] DOM 字体加载失败，但 opentype.js 加载成功:', domError)
    }

    setFont(font)
  }

  useEffect(() => {
    if (font) continueRender(handle)
  }, [font])

  useEffect(() => {
    void loadFont(overlay.src, overlay.localSrc, overlay.styles.fontFamily)
  }, [overlay.src, overlay.localSrc, overlay.styles.fontFamily])

  if (!font) return null

  return (
    <OpentypeFontBasedTextRenderer
      font={font}
      overlay={overlay}
      containerStyle={containerStyle}
    />
  )
}

export const TextLayerRenderer = React.memo(
  TextLayerRendererImpl,
  (prevProps, nextProps) => {
    const basicPropsEqual = (
      prevProps.overlay.id === nextProps.overlay.id &&
      prevProps.overlay.content === nextProps.overlay.content &&
      prevProps.overlay.src === nextProps.overlay.src &&
      prevProps.overlay.width === nextProps.overlay.width &&
      prevProps.overlay.height === nextProps.overlay.height &&
      prevProps.overlay.left === nextProps.overlay.left &&
      prevProps.overlay.top === nextProps.overlay.top &&
      prevProps.overlay.rotation === nextProps.overlay.rotation
    )

    const stylesEqual = JSON.stringify(prevProps.overlay.styles) === JSON.stringify(nextProps.overlay.styles)

    const containerStyleEqual = JSON.stringify(prevProps.containerStyle) === JSON.stringify(nextProps.containerStyle)

    return basicPropsEqual && stylesEqual && containerStyleEqual
  }
)

export const TextLayerContent: React.FC<TextLayerContentProps> = ({ overlay }) => {
  const animation = useOverlayAnimation(overlay)

  const containerStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    textAlign: overlay.styles.textAlign,
    justifyContent:
      overlay.styles.textAlign === 'center'
        ? 'center'
        : overlay.styles.textAlign === 'right'
          ? 'flex-end'
          : 'flex-start',
    overflow: 'hidden',
    boxSizing: 'border-box',
    ...animation,

  }

  return (
    <TextLayerRenderer
      overlay={overlay}
      containerStyle={containerStyle}
    />
  )
}
