{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": false,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,

    /* Path mapping */
    "baseUrl": ".",
    "paths": {
      "@app/shared/*": ["../../../shared/*"],
      "@clipnest/remotion-shared/*": ["../remotion-shared/*"]
    }
  },
  "include": [
    "**/*.ts",
    "**/*.tsx",
    "vite-env.d.ts"
  ],
  "exclude": [
    "node_modules",
    "dist"
  ]
}
