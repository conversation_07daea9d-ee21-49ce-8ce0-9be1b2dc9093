import { interpolate } from 'remotion'

export type AnimationTemplate = {
  name: string
  preview: string
  isPro?: boolean
  enter: (
    frame: number,
    durationInFrames: number
  ) => {
    transform?: string
    opacity?: number
  }
  exit: (
    frame: number,
    durationInFrames: number
  ) => {
    transform?: string
    opacity?: number
  }
}

export const animationTemplates: Record<string, AnimationTemplate> = {
  fade: {
    name: '淡入淡出',
    preview: '简单的淡入/淡出效果',
    enter: frame => ({
      opacity: interpolate(frame, [0, 15], [0, 1], {
        extrapolateRight: 'clamp',
      }),
    }),
    exit: (frame, duration) => ({
      opacity: interpolate(frame, [duration - 15, duration], [1, 0], {
        extrapolateLeft: 'clamp',
      }),
    }),
  },
  slideRight: {
    name: '滑动',
    preview: '从左侧滑入',
    isPro: true,
    enter: frame => ({
      transform: `translateX(${interpolate(frame, [0, 15], [-100, 0], {
        extrapolateRight: 'clamp',
      })}%)`,
      opacity: interpolate(frame, [0, 15], [0, 1], {
        extrapolateRight: 'clamp',
      }),
    }),
    exit: (frame, duration) => ({
      transform: `translateX(${interpolate(
        frame,
        [duration - 15, duration],
        [0, 100],
        { extrapolateLeft: 'clamp' },
      )}%)`,
      opacity: interpolate(frame, [duration - 15, duration], [1, 0], {
        extrapolateLeft: 'clamp',
      }),
    }),
  },
  scale: {
    name: '缩放',
    preview: '缩放进/出场',
    enter: frame => ({
      transform: `scale(${interpolate(frame, [0, 15], [0, 1], {
        extrapolateRight: 'clamp',
      })})`,
      opacity: interpolate(frame, [0, 15], [0, 1], {
        extrapolateRight: 'clamp',
      }),
    }),
    exit: (frame, duration) => ({
      transform: `scale(${interpolate(
        frame,
        [duration - 15, duration],
        [1, 0],
        { extrapolateLeft: 'clamp' },
      )})`,
      opacity: interpolate(frame, [duration - 15, duration], [1, 0], {
        extrapolateLeft: 'clamp',
      }),
    }),
  },
  bounce: {
    name: '弹跳',
    preview: '带弹性的进入效果',
    isPro: true,
    enter: frame => ({
      transform: `translateY(${interpolate(
        frame,
        [0, 10, 13, 15],
        [100, -10, 5, 0],
        { extrapolateRight: 'clamp' },
      )}px)`,
      opacity: interpolate(frame, [0, 10], [0, 1], {
        extrapolateRight: 'clamp',
      }),
    }),
    exit: (frame, duration) => ({
      transform: `translateY(${interpolate(
        frame,
        [duration - 15, duration - 13, duration - 10, duration],
        [0, 5, -10, 100],
        { extrapolateLeft: 'clamp' },
      )}px)`,
      opacity: interpolate(frame, [duration - 10, duration], [1, 0], {
        extrapolateLeft: 'clamp',
      }),
    }),
  },
  flipX: {
    name: '翻转',
    preview: '围绕X轴的3D翻转',
    isPro: true,
    enter: frame => ({
      transform: `perspective(400px) rotateX(${interpolate(
        frame,
        [0, 15],
        [90, 0],
        { extrapolateRight: 'clamp' },
      )}deg)`,
      opacity: interpolate(frame, [0, 5, 15], [0, 0.7, 1], {
        extrapolateRight: 'clamp',
      }),
    }),
    exit: (frame, duration) => ({
      transform: `perspective(400px) rotateX(${interpolate(
        frame,
        [duration - 15, duration],
        [0, -90],
        { extrapolateLeft: 'clamp' },
      )}deg)`,
      opacity: interpolate(
        frame,
        [duration - 15, duration - 5, duration],
        [1, 0.7, 0],
        {
          extrapolateLeft: 'clamp',
        },
      ),
    }),
  },
  zoomBlur: {
    name: '缩放模糊',
    preview: '带模糊效果的缩放',
    isPro: true,
    enter: frame => ({
      transform: `scale(${interpolate(frame, [0, 15], [1.5, 1], {
        extrapolateRight: 'clamp',
      })})`,
      opacity: interpolate(frame, [0, 15], [0, 1], {
        extrapolateRight: 'clamp',
      }),
      filter: `blur(${interpolate(frame, [0, 15], [10, 0], {
        extrapolateRight: 'clamp',
      })}px)`,
    }),
    exit: (frame, duration) => ({
      transform: `scale(${interpolate(
        frame,
        [duration - 15, duration],
        [1, 1.5],
        { extrapolateLeft: 'clamp' },
      )})`,
      opacity: interpolate(frame, [duration - 15, duration], [1, 0], {
        extrapolateLeft: 'clamp',
      }),
      filter: `blur(${interpolate(frame, [duration - 15, duration], [0, 10], {
        extrapolateLeft: 'clamp',
      })}px)`,
    }),
  },
  slideUp: {
    name: '滑动',
    preview: '从底部滑入的现代效果',
    enter: frame => ({
      transform: `translateY(${interpolate(frame, [0, 15], [30, 0], {
        extrapolateRight: 'clamp',
      })}px)`,
      opacity: interpolate(frame, [0, 15], [0, 1], {
        extrapolateRight: 'clamp',
      }),
    }),
    exit: (frame, duration) => ({
      transform: `translateY(${interpolate(
        frame,
        [duration - 15, duration],
        [0, -30],
        { extrapolateLeft: 'clamp' },
      )}px)`,
      opacity: interpolate(frame, [duration - 15, duration], [1, 0], {
        extrapolateLeft: 'clamp',
      }),
    }),
  },
  snapRotate: {
    name: '急停旋转',
    preview: '快速旋转并回弹',
    isPro: true,
    enter: frame => ({
      transform: `rotate(${interpolate(frame, [0, 8, 12, 15], [-10, 5, -2, 0], {
        extrapolateRight: 'clamp',
      })}deg) scale(${interpolate(frame, [0, 15], [0.8, 1], {
        extrapolateRight: 'clamp',
      })})`,
      opacity: interpolate(frame, [0, 10], [0, 1], {
        extrapolateRight: 'clamp',
      }),
    }),
    exit: (frame, duration) => ({
      transform: `rotate(${interpolate(
        frame,
        [duration - 15, duration - 12, duration - 8, duration],
        [0, -2, 5, -10],
        { extrapolateLeft: 'clamp' },
      )}deg) scale(${interpolate(frame, [duration - 15, duration], [1, 0.8], {
        extrapolateLeft: 'clamp',
      })})`,
      opacity: interpolate(frame, [duration - 10, duration], [1, 0], {
        extrapolateLeft: 'clamp',
      }),
    }),
  },
  glitch: {
    name: '故障',
    preview: '数字化的故障闪动效果',
    isPro: true,
    enter: frame => {
      const progress = interpolate(frame, [0, 15], [0, 1], {
        extrapolateRight: 'clamp',
      })
      // Create glitchy movements at specific keyframes
      const xOffset
        = frame % 3 === 0 ? (Math.random() * 10 - 5) * (1 - progress) : 0
      const yOffset
        = frame % 4 === 0 ? (Math.random() * 8 - 4) * (1 - progress) : 0

      return {
        transform: `translate(${xOffset}px, ${yOffset}px) scale(${interpolate(
          frame,
          [0, 3, 6, 10, 15],
          [0.9, 1.05, 0.95, 1.02, 1],
          { extrapolateRight: 'clamp' },
        )})`,
        opacity: interpolate(frame, [0, 3, 5, 15], [0, 0.7, 0.8, 1], {
          extrapolateRight: 'clamp',
        }),
      }
    },
    exit: (frame, duration) => {
      const progress = interpolate(frame, [duration - 15, duration], [0, 1], {
        extrapolateLeft: 'clamp',
      })
      // Create glitchy movements at specific keyframes
      const xOffset
        = (duration - frame) % 3 === 0 ? (Math.random() * 10 - 5) * progress : 0
      const yOffset
        = (duration - frame) % 4 === 0 ? (Math.random() * 8 - 4) * progress : 0

      return {
        transform: `translate(${xOffset}px, ${yOffset}px) scale(${interpolate(
          frame,
          [duration - 15, duration - 10, duration - 6, duration - 3, duration],
          [1, 1.02, 0.95, 1.05, 0.9],
          { extrapolateLeft: 'clamp' },
        )})`,
        opacity: interpolate(
          frame,
          [duration - 15, duration - 5, duration - 3, duration],
          [1, 0.8, 0.7, 0],
          {
            extrapolateLeft: 'clamp',
          },
        ),
      }
    },
  },
  swipeReveal: {
    name: '滑动揭示',
    preview: '通过滑动显示内容',
    isPro: true,
    enter: frame => ({
      transform: `translateX(${interpolate(frame, [0, 15], [0, 0], {
        extrapolateRight: 'clamp',
      })}px)`,
      opacity: 1,
      clipPath: `inset(0 ${interpolate(frame, [0, 15], [100, 0], {
        extrapolateRight: 'clamp',
      })}% 0 0)`,
    }),
    exit: (frame, duration) => ({
      transform: `translateX(${interpolate(
        frame,
        [duration - 15, duration],
        [0, 0],
        { extrapolateLeft: 'clamp' },
      )}px)`,
      opacity: 1,
      clipPath: `inset(0 0 0 ${interpolate(
        frame,
        [duration - 15, duration],
        [0, 100],
        { extrapolateLeft: 'clamp' },
      )}%)`,
    }),
  },
  floatIn: {
    name: '漂浮',
    preview: '平滑的漂浮进入效果',
    enter: frame => ({
      transform: `translate(${interpolate(frame, [0, 15], [10, 0], {
        extrapolateRight: 'clamp',
      })}px, ${interpolate(frame, [0, 15], [-20, 0], {
        extrapolateRight: 'clamp',
      })}px)`,
      opacity: interpolate(frame, [0, 15], [0, 1], {
        extrapolateRight: 'clamp',
      }),
    }),
    exit: (frame, duration) => ({
      transform: `translate(${interpolate(
        frame,
        [duration - 15, duration],
        [0, -10],
        { extrapolateLeft: 'clamp' },
      )}px, ${interpolate(frame, [duration - 15, duration], [0, -20], {
        extrapolateLeft: 'clamp',
      })}px)`,
      opacity: interpolate(frame, [duration - 15, duration], [1, 0], {
        extrapolateLeft: 'clamp',
      }),
    }),
  },
}
