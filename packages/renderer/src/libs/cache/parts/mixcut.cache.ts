import { ICacheManager, SubCacheManager } from '../types'
import { Mixcut } from '@/types/mixcut'
import { RenderRequestPayload } from '@app/shared/types/ipc/mixcut'

export class MixcutCacheManager extends SubCacheManager {

  constructor(parent: ICacheManager) {
    super(parent, 'mixcut_cache', '混剪缓存')
  }

  async cacheMixcut(...mixcuts: Mixcut.SavedMixcut[]): Promise<void> {
    // 过滤出需要缓存的混剪（未缓存的）
    const mixcutsToCache: Array<{ id: number; url: string }> = []

    for (const mixcut of mixcuts) {
      const { id, url } = mixcut

      if (!url) {
        console.warn(`[MixcutCacheManager] 混剪 ${id} 缺少 URL，跳过缓存`)
        continue
      }

      // 检查是否已经缓存
      const existingCache = await this.store.getItem(id.toString())
      if (existingCache) {
        continue
      }

      mixcutsToCache.push({ id, url })
    }

    if (mixcutsToCache.length === 0) {
      return
    }

    try {
      /*const result = */await window.mixcut.cacheMixcutData({ mixcuts: mixcutsToCache })
    } catch (error) {
      console.error('[MixcutCacheManager] 调用主进程缓存接口失败:', error)
      throw error
    }
  }

  /**
   * 获取解密后的混剪 JSON 数据
   * @param id 混剪 ID
   * @returns 解密后的 JSON 数据，如果未缓存则返回 null
   */
  async getMixcutData(id: number): Promise<RenderRequestPayload | null> {
    try {
      return window.mixcut.getMixcutDataFromCache({ id })
    } catch (error) {
      console.error(`[MixcutCacheManager] 获取混剪数据失败 (ID: ${id}):`, error)
      return null
    }
  }

  clearMixcuts(...ids: number[]) {
    return window.mixcut.clearMixcuts(...ids)
  }

  async cleanup(now: number, maxAge: number) {
    const mixcutKeysToRemove: string[] = []
    await this.store.iterate((mixcut: Mixcut.SavedMixcut, key: string) => {
      if (now - mixcut.createAt > maxAge) {
        mixcutKeysToRemove.push(key)
      }
    })

    for (const key of mixcutKeysToRemove) {
      await this.store.removeItem(key)
    }
  }
}
