import { ResourceCacheEntry, ResourceCacheType } from '@app/shared/types/resource-cache.types'
import { SubCacheManager } from '../types'
import { hashUrl } from '../utils/hash'

export class ResourceCacheManager extends SubCacheManager {

  // 内存缓存，用于同步访问，使用与主进程一致的数据结构
  private memoryCache: Map<string, ResourceCacheEntry> = new Map()

  // 初始化状态标记
  private initialized = false

  /**
   * 生成缓存键 - 与主进程保持一致，使用 hashUrl
   */
  private getCacheKey(url: string): string {
    return hashUrl(url)
  }

  /**
   * 初始化缓存 - 从主进程同步数据
   * 公开方法，供缓存初始化器调用
   */
  public async initializeCache(): Promise<void> {
    if (this.initialized) {
      return
    }

    try {
      console.log('[ResourceCacheManager] 开始从主进程同步缓存数据...')

      // 通过 IPC 获取主进程的所有缓存条目
      const allEntries = await window.resource.getAllResources()

      // 清空内存缓存
      this.memoryCache.clear()

      // 将主进程数据同步到内存缓存
      for (const entry of allEntries) {
        this.memoryCache.set(entry.key, entry)
      }

      this.initialized = true
      console.log(`[ResourceCacheManager] 缓存同步完成，共同步 ${allEntries.length} 个条目`)
    } catch (error) {
      console.error('[ResourceCacheManager] 缓存初始化失败:', error)
      throw error
    }
  }

  /**
   * 缓存资源 - 通过主进程获取并更新内存缓存
   */
  public async cacheResource(type: ResourceCacheType, url: string, version?: string, customExt?: string): Promise<string> {
    // 确保缓存已初始化
    await this.initializeCache()

    const key = this.getCacheKey(url)
    const existingEntry = this.memoryCache.get(key)

    // 检查是否已缓存且版本匹配
    if (existingEntry && existingEntry.version === (version || '1.0')) {
      return existingEntry.localPath
    }

    // 通过 IPC 调用主进程获取资源
    const localPath = await window.resource.fetchOrSaveResource({
      url,
      type,
      version: version || '1.0',
      customExt
    })

    const updatedEntry = await window.resource.getResource({ url })
    if (updatedEntry) {
      this.memoryCache.set(updatedEntry.key, updatedEntry)
    }

    return localPath
  }

  public async waitForCachedResource(type: ResourceCacheType, url: string, timeout = 10_000): Promise<string> {
    const pollFetch = async () => {
      const cache = await this.getResourceCache(type, url)
      if (cache) return cache.localPath

      return new Promise<string>(resolve => setTimeout(() => resolve(pollFetch()), 500))
    }

    return Promise.race([
      new Promise<string>((_, reject) => {
        setTimeout(() => reject(new Error('等待资源加载超时')), timeout)
      }),
      pollFetch()
    ])
  }

  /**
   * 获取资源缓存条目
   */
  async getResourceCache(type: ResourceCacheType, url: string): Promise<ResourceCacheEntry | null> {
    await this.initializeCache()

    const key = this.getCacheKey(url)
    const entry = this.memoryCache.get(key)

    // 验证资源类型是否匹配
    if (entry && entry.type === type) {
      return entry
    }

    return null
  }

  /**
   * 同步获取资源的本地路径
   */
  getResourcePathSync(type: ResourceCacheType, url: string): string | null {
    const key = this.getCacheKey(url)
    const entry = this.memoryCache.get(key)

    // 验证资源类型是否匹配
    if (entry && entry.type === type) {
      return entry.localPath
    }

    return null
  }

  /**
   * 同步检查资源是否已缓存
   */
  isCachedSync(type: ResourceCacheType, url: string): boolean {
    const key = this.getCacheKey(url)
    const entry = this.memoryCache.get(key)

    // 验证资源类型是否匹配
    return entry !== undefined && entry.type === type
  }

  /**
   * 获取特定类型的所有资源缓存
   */
  async getResourceCacheByType(type: ResourceCacheType): Promise<Record<string, string>> {
    await this.initializeCache()

    const result: Record<string, string> = {}

    for (const entry of this.memoryCache.values()) {
      if (entry.type === type) {
        result[entry.url] = entry.localPath
      }
    }

    return result
  }

  /**
   * 清除资源缓存 - 注意：这只清除内存缓存，实际文件清理由主进程负责
   */
  async clearResourceCache(type?: ResourceCacheType, url?: string): Promise<void> {
    await this.initializeCache()

    if (type && url) {
      // 清除特定资源
      const key = this.getCacheKey(url)
      const entry = this.memoryCache.get(key)
      if (entry && entry.type === type) {
        this.memoryCache.delete(key)
      }
    } else if (type) {
      // 清除特定类型的所有资源
      const keysToRemove: string[] = []

      for (const [key, entry] of this.memoryCache.entries()) {
        if (entry.type === type) {
          keysToRemove.push(key)
        }
      }

      for (const key of keysToRemove) {
        this.memoryCache.delete(key)
      }
    } else {
      // 清除所有资源
      this.memoryCache.clear()
      this.initialized = false
    }
  }

  override init() {
    // 异步初始化缓存数据
    void this.initializeCache().catch(error => {
      console.error('[ResourceCacheManager] 初始化失败:', error)
    })
  }

  override async cleanup() {
    // 渲染进程不需要执行清理操作，清理由主进程负责
    // 这里只是为了满足接口要求，实际清理逻辑在主进程中
    console.log('[ResourceCacheManager] 清理操作由主进程负责，跳过渲染进程清理')
  }
}
