import axiosInstance from './instance'
import type { HttpService, RequestConfig } from './types'
import { PaginatedResult, PaginationParams } from '@app/shared/infra/request'

/**
 * HTTP 请求服务
 */
const request: HttpService = {
  /**
   * GET 请求
   * @param url 请求地址
   * @param params 请求参数
   * @param config 请求配置
   * @returns Promise
   */
  get: <T = any>(url: string, params?: any, config?: RequestConfig) => {
    return axiosInstance.get<any, T>(url, { params, ...config })
  },

  /**
   * POST 请求
   * @param url 请求地址
   * @param data 请求数据
   * @param config 请求配置
   * @returns Promise
   */
  post: <T = any>(url: string, data?: any, config?: RequestConfig) => {
    return axiosInstance.post<any, T>(url, data, config)
  },

  /**
   * PUT 请求
   * @param url 请求地址
   * @param data 请求数据
   * @param config 请求配置
   * @returns Promise
   */
  put: <T = any>(url: string, data?: any, config?: RequestConfig) => {
    return axiosInstance.put<any, T>(url, data, config)
  },

  /**
   * DELETE 请求
   * @param url 请求地址
   * @param params 请求参数
   * @param config 请求配置
   * @returns Promise
   */
  delete: <T = any>(url: string, params?: any, config?: RequestConfig) => {
    return axiosInstance.delete<any, T>(url, { params, ...config })
  },

  /**
   * PATCH 请求
   * @param url 请求地址
   * @param data 请求数据
   * @param config 请求配置
   * @returns Promise
   */
  patch: <T = any>(url: string, data?: any, config?: RequestConfig) => {
    return axiosInstance.patch<any, T>(url, data, config)
  },

  /**
   * 自定义请求
   * @param config 请求配置
   * @returns Promise
   */
  request: <T = any>(config: RequestConfig) => {
    return axiosInstance.request<any, T>(config)
  }
}

/**
 * 分页请求
 * @param url 请求地址
 * @param params 分页参数
 * @param config 请求配置
 * @returns 分页数据
 */
export const fetchPagination = <T = any>(
  url: string,
  params: PaginationParams,
  config?: RequestConfig
): Promise<PaginatedResult<T>> => {
  return request.post<PaginatedResult<T>>(url, params, config)
}

/**
 * 分页请求
 * @param url 请求地址
 * @param params 分页参数
 * @param config 请求配置
 * @returns 分页数据
 */
export const fetchPaginationGet = <T = any>(
  url: string,
  config?: RequestConfig
) => (
  params: PaginationParams
): Promise<PaginatedResult<T>> => {
  return request.get<PaginatedResult<T>>(url, params, config)
}

function factory(key: keyof HttpService) {
  return <TRequest extends Record<string, any> = object, TResponse = any>(url: string, config?: RequestConfig) => {
    return (params: TRequest) => request[key]<TResponse>(url, params, config)
  }
}

export const requestCurrying = {
  get: factory('get'),
  post: factory('post'),
  delete: factory('delete'),
  put: factory('put'),
  patch: factory('patch')
}

// 导出默认请求对象
export default request
