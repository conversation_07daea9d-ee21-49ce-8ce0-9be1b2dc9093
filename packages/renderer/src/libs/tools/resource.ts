import { rawInstance } from '@/libs/request/instance'

export async function parseUrlFromATag(aTagStr: string) {
  const [dom, parseError] = await new Promise<[Document, null] | [null, Error]>(
    (resolve, reject) => {
      try {
        return resolve([
          new DOMParser().parseFromString(aTagStr, 'text/html'),
          null
        ])
      } catch {
        return reject([
          null,
          new Error(`Failed to parse DOM from response data "${aTagStr}"`)
        ])
      }
    }
  )

  if (parseError) throw parseError

  const aTag = dom.querySelector('a')
  if (!aTag) throw new Error('No valid <a> tag in parsed dom')

  const href = aTag.href
  if (!href) throw new Error('No content in `href` of parsed <a> tag')

  return href
}

export async function parseUrlFromObjectHref(objectHref: string) {
  if (!/\/oss\/object-href\/\w+/.test(objectHref)) {
    return objectHref
  }

  const [fetchResult, fetchError] = await rawInstance
    .get(objectHref)
    .then(r => [r.data, null])
    .catch(error => [null, new Error(`Cannot fetch object href(${objectHref}): ${error.message}`)])

  if (fetchError) throw fetchError

  return parseUrlFromATag(fetchResult)
}

export function extractObjectIdFromObjectHref(href: string) {
  const match = href.match(/\/oss\/object-href\/(\w+)$/)
  return match ? match[1] : null
}
