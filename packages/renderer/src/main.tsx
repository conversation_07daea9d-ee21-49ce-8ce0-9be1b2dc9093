import React from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ErrorHandlerProvider } from '@/contexts'
import { ModalProvider } from './libs/tools/modal'
import '@/libs/cache/cache-initializer'
import { initUnifiedRequestState } from '@/libs/request/session/init-ipc-state'
import { UploadTasksProvider } from '@/modules/task-center/context/provider'

// 设置默认暗色主题
const savedTheme = localStorage.getItem('theme')

const isDark = savedTheme ? savedTheme === 'dark' : true

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      refetchInterval: false,
      staleTime: Infinity

    },
  },
})

if (isDark) {
  document.documentElement.classList.add('dark')
} else {
  document.documentElement.classList.remove('dark')
}

initUnifiedRequestState().catch(console.error)

createRoot(document.getElementById('root')!).render(
  <QueryClientProvider client={queryClient}>
    <ModalProvider>
      <ErrorHandlerProvider>
        <UploadTasksProvider>
          <App />
        </UploadTasksProvider>
      </ErrorHandlerProvider>
    </ModalProvider>
  </QueryClientProvider>
)
