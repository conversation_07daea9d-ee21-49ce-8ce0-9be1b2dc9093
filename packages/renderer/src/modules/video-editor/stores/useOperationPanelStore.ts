import { create } from 'zustand'
import { ScriptSceneData } from '@/hooks/queries/useQueryScript'
import { TextOverlay } from '@app/shared/types/overlay'
import { IndexableTrack } from '@/modules/video-editor/types'

export type OperationPanels =
  | 'text-to-speech'
  | 'global-config'

type TextToSpeechDatasource = ScriptSceneData[] | (TextOverlay & { currentTrack: IndexableTrack })

type OperationPanelStoreStates = {
  activePanel: OperationPanels
  textToSpeechDatasource: TextToSpeechDatasource

  openTextToSpeechPanel(datasource: TextToSpeechDatasource): void
  reset(): void
}

export const useOperationPanelStore = create<OperationPanelStoreStates>(
  (set, _get) => ({
    activePanel: 'global-config',
    textToSpeechDatasource: [],

    openTextToSpeechPanel(datasource) {
      set({ activePanel: 'text-to-speech', textToSpeechDatasource: datasource })
    },
    reset() {
      set({ activePanel: 'global-config', textToSpeechDatasource: [] })
    }
  })
)
