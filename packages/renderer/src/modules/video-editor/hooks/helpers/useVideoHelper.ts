import { useCallback } from 'react'
import { OverlayType, VideoOverlay } from '@app/shared/types/overlay'
import { toast } from 'react-toastify'
import { useEditorContext, useTimeline } from '@/modules/video-editor/contexts'
import {
  byStartFrame,
  byStoryboard,
  findOverlayStoryboard,
  findTrackByOverlay,
  getOverlayTimeRange
} from '@/modules/video-editor/utils/overlay-helper'
import { generateNewOverlayId } from '@/modules/video-editor/utils/track-helper'
import { FPS } from '@/modules/video-editor/constants'
import { cloneDeep } from 'lodash'

/**
 * 计算视频的实际播放时长（考虑变速、去片头、去片尾）
 * @param overlay 视频 Overlay
 * @returns 实际播放时长（帧数）
 */
function calculateActualPlaybackDuration(overlay: VideoOverlay): number {
  const originalDuration = overlay.originalDurationFrames || overlay.durationInFrames
  const trimStart = overlay.trimStartFrames || 0
  const trimEnd = overlay.trimEndFrames || 0
  const speed = overlay.speed || 1

  // 计算去除片头片尾后的时长
  const trimmedDuration = originalDuration - trimStart - trimEnd

  // 考虑播放速度的影响：实际播放时长 = 原始时长 / 播放速度
  const actualDuration = trimmedDuration / speed

  return Math.max(0, actualDuration)
}

/**
 * 计算优化后的切片信息
 * @param originalDurationInFrames 视频原始时长（帧数）
 * @param sliceDurationInFrames 期望的切片时长（帧数）
 * @returns 优化后的切片信息
 */
export function calculateOptimizedSlices(originalDurationInFrames: number, sliceDurationInFrames: number) {
  const minSliceDuration = FPS // 1 秒的帧数
  let sliceCount = Math.ceil(originalDurationInFrames / sliceDurationInFrames)

  if (sliceCount <= 1) {
    return {
      sliceCount: 1,
      slices: [{ duration: originalDurationInFrames }],
      lastSliceDurationInSeconds: originalDurationInFrames / FPS
    }
  }

  // 计算最后一个切片的时长
  const lastSliceDuration = originalDurationInFrames % sliceDurationInFrames || sliceDurationInFrames

  const slices: Array<{ duration: number }> = []

  // 如果最后一个切片小于1秒，则合并到倒数第二个切片
  if (lastSliceDuration < minSliceDuration && sliceCount > 1) {
    sliceCount -= 1

    // 前面的切片保持原始时长
    for (let i = 0; i < sliceCount - 1; i++) {
      slices.push({ duration: sliceDurationInFrames })
    }

    // 最后一个切片包含原本的倒数第二个和最后一个切片
    const lastSliceExtendedDuration = sliceDurationInFrames + lastSliceDuration
    slices.push({ duration: lastSliceExtendedDuration })

    return {
      sliceCount,
      slices,
      lastSliceDurationInSeconds: lastSliceExtendedDuration / FPS
    }
  }

  // 正常情况，不需要合并
  for (let i = 0; i < sliceCount - 1; i++) {
    slices.push({ duration: sliceDurationInFrames })
  }
  slices.push({ duration: lastSliceDuration })

  return {
    sliceCount,
    slices,
    lastSliceDurationInSeconds: lastSliceDuration / FPS
  }
}

type VideoHelper = {
  /**
   * 翻转指定的视频 Overlay
   * @param overlay 需要翻转的视频 Overlay
   * @param copyOnly 为 `true` 时表示镜像后复制到剪贴板, 不会修改原 Overlay; 否则直接修改原 Overlay. 默认值: `false`
   */
  flipCurrentOverlay(overlay: VideoOverlay, copyOnly?: boolean): void

  /**
   * 变速视频以自动对齐到分镜时长
   */
  autoAdjustVideoSpeed(overlay: VideoOverlay): void

  /**
   * 自动切片视频
   * @param overlay 需要切片的视频 Overlay
   * @param sliceDurationInFrames 切片时长（帧数）
   */
  autoSliceVideo(overlay: VideoOverlay, sliceDurationInFrames: number): void
}

export const useVideoHelper = (): VideoHelper => {
  const {
    tracks,
    updateOverlay,
    updateTracks,
  } = useEditorContext()

  const { clipboard } = useTimeline()

  const flipCurrentOverlay = useCallback(
    (overlay: VideoOverlay, copyOnly = false) => {
      if (overlay.type !== OverlayType.VIDEO) return

      const transformWhenFlipped = 'scaleX(-1)'

      const updated: VideoOverlay = {
        ...overlay,
        styles: {
          ...overlay.styles,
          transform: overlay.styles?.transform === transformWhenFlipped
            ? ''
            : transformWhenFlipped
        }
      }

      if (copyOnly) {
        clipboard.copyOverlay(updated)
        toast('已复制镜像后的视频')
        return
      }

      return updateOverlay(overlay.id, () => updated)
    },
    [clipboard]
  )

  const autoAdjustVideoSpeed = useCallback((overlay: VideoOverlay) => {
    if (overlay.type !== OverlayType.VIDEO) return

    const videoOverlay = overlay as VideoOverlay
    const storyboard = findOverlayStoryboard(tracks, videoOverlay)
    if (!storyboard) {
      toast.error('无法找到视频所在的分镜')
      return
    }

    // 检查必要的属性
    if (!videoOverlay.originalDurationFrames || videoOverlay.originalDurationFrames <= 0) {
      toast.error('视频原始时长信息缺失，无法进行自动变速')
      return
    }

    const storyboardDuration = storyboard.durationInFrames
    if (storyboardDuration <= 0) {
      toast.error('分镜时长无效，无法进行自动变速')
      return
    }

    return updateTracks(prevTracks => {
      // 找到目标视频所在的轨道
      const targetTrack = findTrackByOverlay(prevTracks, videoOverlay.id)
      if (!targetTrack) return prevTracks

      // 查找同一分镜和轨道下的所有其他视频 overlay
      const sameTrackVideos = targetTrack.overlays
        .filter(byStoryboard(storyboard))
        .filter(o => o.type === OverlayType.VIDEO && o.id !== videoOverlay.id)
        .sort(byStartFrame())

      // 计算其他视频的总时长
      const otherVideosTotalDuration = sameTrackVideos.reduce((total, video) => {
        return total + video.durationInFrames
      }, 0)

      // 计算目标视频需要的新时长
      const targetNewDuration = storyboardDuration - otherVideosTotalDuration

      if (targetNewDuration <= 0) {
        toast.error('分镜剩余时长不足，无法调整视频速度')
        return prevTracks
      }

      // 计算新的播放速度
      const newSpeed = videoOverlay.originalDurationFrames / targetNewDuration

      const finalDuration = videoOverlay.originalDurationFrames / newSpeed

      // 创建更新后的轨道数组
      const updatedTracks = [...prevTracks]
      const updatedTrack = { ...targetTrack }
      const updatedOverlays = [...updatedTrack.overlays]

      // 找到目标视频在轨道中的索引
      const videoIndex = updatedOverlays.findIndex(o => o.id === videoOverlay.id)
      if (videoIndex === -1) return prevTracks

      // 更新目标视频的属性
      const updatedVideo: VideoOverlay = {
        ...videoOverlay,
        speed: newSpeed,
        durationInFrames: Math.round(finalDuration)
      }

      updatedOverlays[videoIndex] = updatedVideo

      // 计算时长变化量
      const durationChange = updatedVideo.durationInFrames - videoOverlay.durationInFrames

      // 如果时长发生变化，需要调整后续视频的位置
      if (durationChange !== 0) {
        const [, videoEndFrame] = getOverlayTimeRange(videoOverlay)

        // 找到需要调整位置的后续视频（在同一分镜内且开始时间在当前视频结束时间之后）
        const videosToAdjust = updatedOverlays
          .filter(byStoryboard(storyboard))
          .filter(o => o.from >= videoEndFrame)
          .sort(byStartFrame())

        // 调整后续视频的开始时间
        videosToAdjust.forEach(video => {
          const videoIndexToUpdate = updatedOverlays.findIndex(o => o.id === video.id)
          if (videoIndexToUpdate !== -1) {
            updatedOverlays[videoIndexToUpdate] = {
              ...video,
              from: video.from + durationChange
            }
          }
        })
      }

      // 更新轨道
      updatedTrack.overlays = updatedOverlays
      updatedTracks[targetTrack.index] = updatedTrack

      return updatedTracks
    })
  }, [tracks, updateTracks])

  const autoSliceVideo = useCallback((overlay: VideoOverlay, sliceDurationInFrames: number) => {
    if (overlay.type !== OverlayType.VIDEO) return

    // 检查必要的属性
    if (!overlay.originalDurationFrames || overlay.originalDurationFrames <= 0) {
      toast.error('视频原始时长信息缺失，无法进行自动切片')
      return
    }

    if (sliceDurationInFrames <= 0) {
      toast.error('切片时长必须大于 0')
      return
    }

    // 计算实际播放时长
    const actualDurationInFrames = calculateActualPlaybackDuration(overlay)

    if (sliceDurationInFrames > actualDurationInFrames) {
      toast.error('切片时长不能大于视频实际播放时长')
      return
    }

    const minSliceDuration = FPS // 1 秒的帧数
    if (sliceDurationInFrames < minSliceDuration) {
      toast.error('切片时长不能小于 1 秒')
      return
    }

    return updateTracks(prevTracks => {
      // 找到目标视频所在的轨道
      const targetTrack = findTrackByOverlay(prevTracks, overlay.id)
      if (!targetTrack) {
        toast.error('无法找到视频所在的轨道')
        return prevTracks
      }

      // 使用实际播放时长进行切片计算
      const sliceInfo = calculateOptimizedSlices(actualDurationInFrames, sliceDurationInFrames)

      if (sliceInfo.sliceCount <= 1) {
        toast.info('视频时长不足以进行切片')
        return prevTracks
      }

      const clonedTracks = cloneDeep(prevTracks)
      const updatedTrack = clonedTracks[targetTrack.index]

      // 找到原视频在轨道中的索引
      const originalVideoIndex = updatedTrack.overlays.findIndex(o => o.id === overlay.id)
      if (originalVideoIndex === -1) return prevTracks

      // 生成切片
      const slices: VideoOverlay[] = []
      let currentFrom = overlay.from
      const speed = overlay.speed || 1
      const trimStart = overlay.trimStartFrames || 0
      let accumulatedOriginalDuration = 0 // 累计的原始视频时长

      // 预先生成所有需要的新 ID
      const baseNewId = generateNewOverlayId(prevTracks)
      const newIds = Array.from({ length: sliceInfo.sliceCount - 1 }, (_, i) => baseNewId + i)

      for (let i = 0; i < sliceInfo.sliceCount; i++) {
        const currentSliceDurationInActualTime = sliceInfo.slices[i].duration
        const sliceId = i === 0 ? overlay.id : newIds[i - 1]

        // 将实际播放时长转换为原始视频时间轴上的时长
        // 原始时长 = 实际播放时长 * 播放速度
        const originalSliceDuration = currentSliceDurationInActualTime * speed

        // 计算在原始视频时间轴上的起始位置
        const originalVideoStartTime = trimStart + accumulatedOriginalDuration

        const slice: VideoOverlay = {
          ...overlay,
          id: sliceId,
          from: currentFrom,
          durationInFrames: currentSliceDurationInActualTime, // 时间轴上显示的时长（实际播放时长）
          trimStartFrames: originalVideoStartTime,
          trimEndFrames: Math.max(0, overlay.originalDurationFrames - originalVideoStartTime - originalSliceDuration),
          speed: speed // 保持原有的播放速度
        }

        slices.push(slice)

        // 为下一个切片准备参数
        currentFrom += currentSliceDurationInActualTime
        accumulatedOriginalDuration += originalSliceDuration
      }

      // 替换原视频为切片
      updatedTrack.overlays.splice(originalVideoIndex, 1, ...slices)

      // 处理后续 Overlay 的位置调整
      const originalVideoEnd = overlay.from + overlay.durationInFrames
      const lastSliceEnd = slices[slices.length - 1].from + slices[slices.length - 1].durationInFrames
      const positionShift = lastSliceEnd - originalVideoEnd

      if (positionShift !== 0) {
        // 调整后续 Overlay 的位置
        updatedTrack.overlays.forEach(o => {
          if (o.from >= originalVideoEnd && !slices.some(slice => slice.id === o.id)) {
            o.from += positionShift
          }
        })
      }

      toast.success(`视频已成功切片为 ${sliceInfo.sliceCount} 个片段`)
      return clonedTracks
    })
  }, [tracks, updateTracks])

  return {
    flipCurrentOverlay,
    autoAdjustVideoSpeed,
    autoSliceVideo
  }
}
