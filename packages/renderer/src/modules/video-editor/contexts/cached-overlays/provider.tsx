import React, { PropsWithChildren, useCallback, useEffect, useMemo, useState } from 'react'
import {
  CachedOverlaysContext as CachedOverlaysContext,
  CachedOverlaysContextValues,
  useEditorContext,
  useTimeline
} from '@/modules/video-editor/contexts'
import { merge } from 'lodash'

import { RenderableOverlay } from '@app/shared/types/overlay'
import { calculateRenderableOverlays } from '@/modules/video-editor/utils/overlay-helper'
import { TrackType } from '@/modules/video-editor/types'

const useRenderableOverlays = () => {
  const { tracks } = useEditorContext()
  const { videoActivation, narrationActivation } = useTimeline()

  return useMemo(
    (): RenderableOverlay[] => calculateRenderableOverlays(
      tracks, {
        [TrackType.VIDEO]: videoActivation,
        [TrackType.NARRATION]: narrationActivation
      },
      false
    ),
    [tracks, videoActivation, narrationActivation]
  )
}

export const CachedOverlaysProvider: React.FC<PropsWithChildren> = ({ children }) => {
  const { updateOverlay } = useEditorContext()
  const renderableOverlays = useRenderableOverlays()

  const [localOverlays, setLocalOverlays] = useState<RenderableOverlay[]>(renderableOverlays)

  useEffect(() => {
    setLocalOverlays(renderableOverlays)
  }, [renderableOverlays])

  const handleUpdateOverlay = useCallback<CachedOverlaysContextValues['requestUpdate']>(
    (id, updater, commit = false) => {
      const index = localOverlays.findIndex(o => o.id === id)
      if (index === -1) return localOverlays
      const target = localOverlays[index]!

      const updatedOverlay = (
        typeof updater === 'function'
          ? updater(target as any)
          : merge({}, target, updater)
      ) as RenderableOverlay

      if (commit) {
        updateOverlay(updatedOverlay.id, () => updatedOverlay)
      }

      setLocalOverlays([
        ...localOverlays.slice(0, index),
        updatedOverlay,
        ...localOverlays.slice(index + 1)
      ])
    },
    [localOverlays]
  )

  return (
    <CachedOverlaysContext
      value={{
        overlays: localOverlays,
        requestUpdate: handleUpdateOverlay
      }}
    >
      {children}
    </CachedOverlaysContext>
  )
}
