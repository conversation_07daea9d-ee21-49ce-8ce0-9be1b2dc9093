import { createContext, useContext } from 'react'
import { ResourcePlugins } from '@/modules/video-editor/resource-plugin-system'

export type EditorSidebarContextValues = {
  activeResourcePanel: ResourcePlugins
  setActiveResourcePanel: (panel: ResourcePlugins) => void
}

export const EditorSidebarContext = createContext<EditorSidebarContextValues | undefined>(undefined)

export const useEditorSidebar = () => {
  const context = useContext(EditorSidebarContext)

  if (!context) {
    throw new Error('useSidebar must be used within a SidebarProvider')
  }

  return context
}
