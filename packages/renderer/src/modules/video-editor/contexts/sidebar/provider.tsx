import React, { useState } from 'react'
import { EditorSidebarContext } from './context'
import { ResourcePlugins } from '@/modules/video-editor/resource-plugin-system'

export const SidebarProvider: React.FC<React.PropsWithChildren> = ({
  children,
}) => {
  const [activeResourcePanel, setActiveResourcePanel] = useState<ResourcePlugins>(null as any)

  return (
    <EditorSidebarContext.Provider
      value={{
        activeResourcePanel,
        setActiveResourcePanel,
      }}
    >
      {children}
    </EditorSidebarContext.Provider>
  )
}
