import React from 'react'
import { formatDistanceToNow } from 'date-fns'

import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { cacheManager } from '@/libs/cache/cache-manager'
import { toast } from 'react-toastify'

interface AutosaveRecoveryDialogProps {
  /**
   * Project ID of the autosave
   */
  projectId: string

  /**
   * Timestamp of when the autosave was created
   */
  timestamp: number

  /**
   * Function to call when user chooses to recover the autosave
   */
  onRecover: () => Promise<void>

  /**
   * Function to call when user chooses to discard the autosave
   */
  onDiscard: () => void

  /**
   * Function to call when dialog is closed
   */
  onClose: () => void

  /**
   * Whether the dialog is open
   */
  open?: boolean
}

/**
 * Dialog component that prompts the user to recover an autosaved project
 */
export const AutosaveRecoveryDialog: React.FC<AutosaveRecoveryDialogProps> = ({
  projectId,
  timestamp,
  onRecover,
  onDiscard,
  onClose,
  open = true,
}) => {
  const [isLoading, setIsLoading] = React.useState(false)
  const relativeTime = formatDistanceToNow(new Date(timestamp), {
    addSuffix: true,
  })

  const handleOpenChange = (isOpen: boolean) => {
    if (!isOpen) {
      onClose()
    }
  }

  const handleRecover = async () => {
    if (isLoading) return

    try {
      setIsLoading(true)
      await onRecover()
      onClose()
    } catch (error) {
      console.error('Failed to recover autosave:', error)
      toast.error('无法恢复自动保存的数据')
    } finally {
      setIsLoading(false)
    }
  }

  const handleDiscard = async () => {
    if (isLoading) return

    try {
      setIsLoading(true)
      await cacheManager.projectState.clearAutosave(projectId)
      onDiscard()
      onClose()
    } catch (error) {
      console.error('Failed to clear autosave:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <div className="h-2 w-2 rounded-full bg-yellow-500 animate-pulse" />
            发现未保存的更改
          </DialogTitle>
          <DialogDescription>
            我们发现了一个自动保存的项目版本，保存时间为{' '}
            <time
              dateTime={new Date(timestamp).toISOString()}
              className="font-medium"
            >
              {relativeTime}
            </time>
            {' '}
            ({new Date(timestamp).toLocaleTimeString()})
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
          <Button
            variant="outline"
            onClick={handleDiscard}
            disabled={isLoading}
            className="mt-2 sm:mt-0"
          >
            丢弃
          </Button>
          <Button
            variant="default"
            onClick={handleRecover}
            disabled={isLoading}
          >
            {isLoading ? '恢复中...' : '恢复更改'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
