import React, { useCallback, useEffect, useRef, useState } from 'react'
import { PlayerRef } from '@remotion/player'
import { FPS } from '../../constants'

export type VideoPlayerHook = {
  /**
   * 每秒帧数
   */
  fps: number

  /**
   * 播放器的当前状态(播放/暂停)
   */
  isPlaying: boolean

  /**
   * 播放器当前播放位置的帧数
   */
  currentFrame: number

  /**
   * 当前播放速率
   */
  playbackRate: number

  /**
   * <VideoPlayer /> 组件的 ref
   */
  playerRef: React.RefObject<any>

  /**
   * 播放/暂停
   */
  togglePlayPause(): void

  /**
   * 调整播放速度倍率
   */
  setPlaybackRate(rate: number): void

  /**
   * 将帧数转换为时间戳
   */
  formatTime(frame: number): string

  /**
   * 跳转到指定帧
   */
  seekTo(frame: number): void

  /**
   * 快进/快退指定秒数
   */
  skip(seconds: number, durationInFrames: number): void

  /**
   * 重置到视频开始位置
   */
  resetToStart(): void
}

/**
 * Custom hook for managing video player functionality
 * @returns An object containing video player controls and state
 */
export const useVideoPlayer = (): VideoPlayerHook => {
  const [fps] = useState(FPS)
  const [isPlaying, setIsPlaying] = useState(false)
  const [playbackRate, setPlaybackRate] = useState(1)
  const [currentFrame, setCurrentFrame] = useState(0)
  const playerRef = useRef<PlayerRef>(null)

  // Frame update effect
  useEffect(() => {
    let animationFrameId: number
    let lastUpdateTime = 0
    const frameInterval = 1000 / fps

    const updateCurrentFrame = () => {
      const now = performance.now()
      if (now - lastUpdateTime >= frameInterval) {
        if (playerRef.current) {
          const frame = Math.round(playerRef.current.getCurrentFrame())
          setCurrentFrame(frame)
        }
        lastUpdateTime = now
      }

      animationFrameId = requestAnimationFrame(updateCurrentFrame)
    }

    // Start the animation frame loop
    animationFrameId = requestAnimationFrame(updateCurrentFrame)

    // Clean up
    return () => {
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId)
      }
    }
  }, [isPlaying, fps])

  /**
   * Toggles between play and pause states
   */
  const togglePlayPause = useCallback(() => {
    if (playerRef.current) {
      if (!isPlaying) {
        playerRef.current.play()
      }
      else {
        playerRef.current.pause()
      }
      setIsPlaying(!isPlaying)
    }
  }, [playerRef, isPlaying])

  /**
   * Converts frame count to formatted time string
   * @param frames - Number of frames to convert
   * @returns Formatted time string in MM:SS format
   */
  const formatTime = useCallback((frames: number) => {
    const totalSeconds = frames / fps
    const minutes = Math.floor(totalSeconds / 60)
    const seconds = totalSeconds - minutes * 60

    return `${minutes.toString().padStart(2, '0')}:${seconds.toFixed(2).padStart(5, '0')}`
  }, [fps])

  /**
   * Seeks to a specific frame in the video
   * @param frame - Target frame number
   */
  const seekTo = (frame: number) => {
    if (playerRef.current) {
      playerRef.current.seekTo(frame)
    }
  }

  const skip = useCallback((seconds: number, durationInFrames: number) => {
    if (!playerRef.current) return

    if (isPlaying) {
      playerRef.current.pause()
      setIsPlaying(!isPlaying)
    }

    const framesToSkip = Math.round(seconds * fps)
    const newFrame = Math.min(
      Math.max(currentFrame + framesToSkip, 0),
      durationInFrames - 1
    )

    seekTo(newFrame)
  }, [currentFrame, fps, seekTo, isPlaying])

  const resetToStart = useCallback(() => {
    seekTo(0)
  }, [seekTo])

  return {
    fps,
    isPlaying,
    playbackRate,
    currentFrame,
    playerRef,
    togglePlayPause,
    formatTime,
    seekTo,
    setPlaybackRate,
    skip,
    resetToStart
  }
}
