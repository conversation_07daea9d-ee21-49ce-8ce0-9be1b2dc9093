import { useState } from 'react'
import type * as CSS from 'csstype'

export type GlobalConfigHook = {
  backgroundColor: CSS.Property.BackgroundColor
  setBackgroundColor: (color: CSS.Property.BackgroundColor) => void
}

export const useGlobalConfig = (): GlobalConfigHook => {
  const [backgroundColor, setBackgroundColor] = useState<CSS.Property.BackgroundColor>('#000')

  return {
    backgroundColor,
    setBackgroundColor,
  }
}
