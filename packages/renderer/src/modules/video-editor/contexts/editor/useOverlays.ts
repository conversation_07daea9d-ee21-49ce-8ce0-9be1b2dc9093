import React, { useCallback, useMemo, useState } from 'react'
import { Overlay } from '@app/shared/types/overlay'
import _, { merge } from 'lodash'
import { findOverlay } from '@/modules/video-editor/utils/overlay-helper'
import {
  calculateTrackAfterOverlayRemoved,
  calculateTracksAfterOverlayUpdated,
  flatOverlaysFromTracks,
  SingleOverlayUpdatePayload,
  TrackCleaningPipelines
} from '@/modules/video-editor/utils/track-helper'
import { useOperationPanelStore } from '@/modules/video-editor/stores/useOperationPanelStore'
import { Track } from '@/modules/video-editor/types'
import { useStateToRef } from '@/hooks/useStateToRef'

export type OverlaysHook = {
  tracks: Track[]

  /**
   * 原始的 setState, 没有任何后置处理
   */
  setTracksDirectly: (tracks: Track[]) => void

  /**
   * 更新 tracks 并自动进行数据检查与规整
   */
  updateTracks: React.Dispatch<React.SetStateAction<Track[]>>

  // 编辑器中所有 overlay 的数组
  overlays: Overlay[]

  /**
   * 当前选中的用于编辑中的 Overlay. 为 `null` 时表示无选中
   */
  selectedOverlay: Overlay | null

  /**
   * 设置当前选中的 Overlay
   * @param overlay 为 Overlay 对象或者其 ID, 或者为 `null` 时表示取消选中
   */
  setSelectedOverlay(overlay: Overlay | number | null): void

  /**
   * 根据 ID 删除指定的 Overlay
   */
  deleteOverlay(id: number): void

  /**
   * 根据 ID 更新指定的 Overlay
   * @PureFunction
   * @param id 要更新的 Overlay 的ID
   * @param updater 当则仅为对象时，覆盖更新对象中存在的字段; 当为函数时则完整覆盖更新
   */
  updateOverlay(
    id: number,
    updater: Partial<SingleOverlayUpdatePayload> | ((overlay: Overlay) => SingleOverlayUpdatePayload)
  ): void

  /**
   * 批量更新多个 overlay
   * @param overlays
   */
  bulkUpdateOverlays(overlays: Array<SingleOverlayUpdatePayload>): void

  resetOverlays(): void
}

const pipeline = _.flow(
  TrackCleaningPipelines.ensureEmptyTracksAndSort,
  TrackCleaningPipelines.filterValidOverlays,
  TrackCleaningPipelines.removeUselessEmptyTracks,
)

/**
 * 管理编辑器中 overlay 元素的 Hook
 * Overlay 可以是文本、视频或音频，它们被定位在时间轴上
 * @returns 包含 overlay 状态和管理函数的对象
 */
export function useOverlays(initialTracks: Track[] = []): OverlaysHook {
  const { reset } = useOperationPanelStore()

  const [tracks, _setTracks] = useState<Track[]>(pipeline(initialTracks))

  const setTracksDirectly = (tracks: Track[]) => {
    return _setTracks(pipeline(tracks))
  }

  const updateTracks = useCallback((updater: React.SetStateAction<Track[]>) => {
    _setTracks(prevTracks => {
      const newTracks = typeof updater === 'function' ? updater(prevTracks) : updater
      return pipeline(newTracks)
    })
  }, [])

  const overlays = useMemo(() => {
    return flatOverlaysFromTracks(tracks)
  }, [tracks])

  // 跟踪当前选中用于编辑的 overlay
  const [selectedOverlay, _setSelectedOverlay] = useState<Overlay | null>(null)
  const selectedOverlayRef = useStateToRef(selectedOverlay)

  const setSelectedOverlay = useCallback((overlay: Overlay | number | null) => {
    reset()
    if (typeof overlay === 'number') {
      _setSelectedOverlay(overlays.find(o => o.id === overlay) || null)
    } else {
      _setSelectedOverlay(overlay)
    }
  }, [overlays])

  /**
   * 更新特定 overlay 的属性
   * 支持直接属性更新和函数式更新
   */
  const updateOverlay = (
    id: number,
    updater: Partial<SingleOverlayUpdatePayload> | ((overlay: Overlay) => SingleOverlayUpdatePayload),
  ) => {
    const updateActiveOverlay = (updatedOverlay: Overlay) => {
      if (selectedOverlayRef.current?.id === updatedOverlay.id) {
        setSelectedOverlay(updatedOverlay)
      }
    }

    return updateTracks(prevTracks => {
      const targetOverlay = findOverlay(prevTracks, id)
      if (!targetOverlay) return prevTracks

      // 计算更新后的 overlay
      const updated = typeof updater === 'function'
        ? updater(targetOverlay)
        : merge({}, targetOverlay, updater) as SingleOverlayUpdatePayload

      updateActiveOverlay(updated)
      const { targetTrackIndex, ...updatedOverlay } = updated
      return calculateTracksAfterOverlayUpdated(prevTracks, updatedOverlay, targetTrackIndex, true)
    })
  }

  /**
   * 一次性批量更新多个 overlay
   * 在单次状态更新中高效地更新多个 overlay
   * @param overlaysToUpdate 要更新的 overlay 数组
   */
  const bulkUpdateOverlays = useCallback(
    (overlaysToUpdate: Array<SingleOverlayUpdatePayload>) => {
      if (!overlaysToUpdate || overlaysToUpdate.length === 0) return

      return updateTracks(prevTracks => {
        return overlaysToUpdate.reduce(
          (result, update) => {
            const { targetTrackIndex, ...updatedOverlay } = update

            return calculateTracksAfterOverlayUpdated(result, updatedOverlay, targetTrackIndex, false)
          },
          prevTracks
        )
      })
    },
    [selectedOverlay]
  )

  /**
   * 通过 ID 移除 overlay 并清除选择
   */
  const deleteOverlay = useCallback((id: number) => {
    updateTracks(prevTracks => {
      const target = findOverlay(prevTracks, id)
      if (!target) return prevTracks

      return prevTracks.map(
        (_, index) => {
          return calculateTrackAfterOverlayRemoved(prevTracks, index, target)
        },
      )
    })
    setSelectedOverlay(null)
  }, [])

  const resetOverlays = useCallback(() => {
    updateTracks(initialTracks)
    setSelectedOverlay(null)
  }, [])

  return {
    tracks,
    setTracksDirectly,
    updateTracks,
    overlays,
    selectedOverlay,
    setSelectedOverlay,
    updateOverlay,
    bulkUpdateOverlays,
    deleteOverlay,
    resetOverlays,
  }
}
