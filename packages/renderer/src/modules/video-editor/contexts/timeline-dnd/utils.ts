import { Overlay, OverlayType } from '@app/shared/types/overlay'
import { IndexableTrack, Track } from '../../types'
import { findOverlay, findOverlayStoryboard, findTrackByOverlay } from '@/modules/video-editor/utils/overlay-helper'
import {
  findStoryboardByFromFrame,
  isOverlayAcceptableByTrack,
  SingleOverlayUpdatePayload
} from '@/modules/video-editor/utils/track-helper'
import { AdjustCalculator } from './adjust-calculator'
import { DraggableState, OverlaysAdjustment } from './types'
import { omit } from 'lodash'

/**
 * 网格对齐函数
 */
export function snapToGrid(value: number) {
  const GRID_SIZE = 1 // 假设帧级别对齐
  return Math.round(value / GRID_SIZE) * GRID_SIZE
}

/**
 * 构建 Overlay 更新数据
 */
export function buildOverlayUpdates(tracks: Track[], overlaysAdjust?: OverlaysAdjustment) {
  if (!overlaysAdjust) return []

  const updates: SingleOverlayUpdatePayload[] = []

  overlaysAdjust.forEach(({
    fromFrameShift = 0,
    durationShift = 0,
    targetStoryboardIndex
  }, overlayId) => {
    const overlayToAdjust = findOverlay(tracks, overlayId)
    if (overlayToAdjust) {
      updates.push({
        /*
          Q: WHY DO THIS?
          A: SingleOverlayUpdatePayload 中的 `targetTrackIndex` 可能会随着 tracks 的状态更新而无意中被设置到 `tracks` 中的 overlay 里
             导致这里出现错误的值
         */
        ...(omit(overlayToAdjust, 'targetTrackIndex') as Overlay),
        from: overlayToAdjust.from + fromFrameShift,
        durationInFrames: overlayToAdjust.durationInFrames + durationShift,
        ...(overlayToAdjust.type !== OverlayType.STORYBOARD && targetStoryboardIndex !== undefined && {
          storyboardIndex: targetStoryboardIndex
        })
      })
    }
  })

  return updates
}

export function calculateDraggableStateForMoving(
  tracks: Track[],
  currentOverlay: Overlay,
  targetStartFrame: number,
  targetTrack: IndexableTrack
): DraggableState {
  if (!currentOverlay || !isOverlayAcceptableByTrack(currentOverlay, targetTrack)) {
    return {
      draggable: false,
    }
  }

  const originalTrack = findTrackByOverlay(tracks, currentOverlay.id) || null

  const targetStoryboard = targetTrack.isGlobalTrack
    ? null
    : findStoryboardByFromFrame(tracks, targetStartFrame)

  return new AdjustCalculator(tracks)
    .calcAdjustForMoving(
      currentOverlay,
      findOverlayStoryboard(tracks, currentOverlay),
      originalTrack,
      targetStoryboard,
      targetTrack,
      targetStartFrame,
    )
}
