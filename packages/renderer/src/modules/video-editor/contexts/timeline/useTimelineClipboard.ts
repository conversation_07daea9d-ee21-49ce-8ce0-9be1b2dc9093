import { useCallback, useState } from 'react'
import { Overlay } from '@app/shared/types/overlay'
import _, { cloneDeep } from 'lodash'
import { useEditorContext } from '@/modules/video-editor/contexts'
import { calculateTrackAfterOverlayPushed, calculateTrackAfterOverlayRemoved } from '../../utils/track-helper'

type ClipboardValue = Overlay & { trackIndex?: number }

export type TimelineClipboard = {
  /** 当前剪贴板中的 overlay */
  value: ClipboardValue | null

  /** 剪贴当前 Overlay 到剪贴板 */
  clipOverlay(overlay: Overlay, trackIndex: number): void

  /** 剪贴当前 Overlay 到剪贴板 */
  copyOverlay(overlay: Overlay): void

  /** 清空剪贴板 */
  clearClipboard(): void

  /**
   * 在指定轨道的指定位置粘贴 overlay
   */
  pasteOverlay(trackIndex: number, startFrame: number): void
}

/**
 * Overlay 剪贴板 Hook
 * 提供复制、粘贴 overlay 的功能
 *
 * @returns OverlayClipboard 对象，包含剪贴板状态和操作方法
 *
 * @example
 * ```tsx
 * const clipboard = useOverlayClipboard()
 *
 * // 复制 overlay
 * clipboard.clipOverlay(selectedOverlay)
 *
 * ```
 */
export const useTimelineClipboard = (): TimelineClipboard => {
  const { updateTracks } = useEditorContext()

  // 剪贴板状态 - 存储被复制的 overlay
  const [value, setValue] = useState<ClipboardValue | null>(null)
  const [from, setFrom] = useState<'clip' | 'copy'>()

  const clipOverlay = (overlay: Overlay, trackIndex: number) => {
    const clippedOverlay = cloneDeep(_.omit(overlay, 'trackIndex')) as Overlay
    setValue({ ...clippedOverlay, trackIndex })
    setFrom('clip')
  }

  const copyOverlay = (overlay: Overlay) => {
    const clippedOverlay = cloneDeep(_.omit(overlay, 'trackIndex')) as Overlay
    setValue(clippedOverlay)
    setFrom('copy')
  }

  /**
   * 清空剪贴板
   */
  const clearClipboard = () => setValue(null)

  const pasteOverlay = useCallback(
    (targetTrackIndex: number, startFrame: number) => {
      if (!value) return

      const { trackIndex, ...overlayToPaste } = value

      updateTracks(prevTracks => (
        prevTracks.map((track, trackIndex) => {
          if (from === 'clip' && track.overlays.some(o => o.id === overlayToPaste.id)) {
            track = calculateTrackAfterOverlayRemoved(prevTracks, trackIndex, overlayToPaste)
          }

          if (trackIndex === targetTrackIndex) {
            const updatedTracks = [
              ...prevTracks.slice(0, targetTrackIndex),
              track,
              ...prevTracks.slice(targetTrackIndex + 1)
            ]

            const [newTrack] = calculateTrackAfterOverlayPushed(
              updatedTracks,
              targetTrackIndex,
              overlayToPaste,
              startFrame
            )
            track = newTrack
          }

          return track
        })
      ))

      if (from === 'clip') setValue(null)
    },
    [value, from]
  )

  return {
    value,
    clipOverlay,
    copyOverlay,
    clearClipboard,
    pasteOverlay
  }
}
