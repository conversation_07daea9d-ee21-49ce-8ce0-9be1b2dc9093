import React, { memo, useCallback, useEffect, useMemo, useState } from 'react'
import {
  useInfiniteQueryLocalPasterList,
  useInfiniteQueryPasterUnified,
  useQueryPasterCategory,
  useQueryPasterDirList
} from '@/hooks/queries/useQueryPaster'
import { PasterResource, ResourceSource } from '@/types/resources'
import { ResourcePanelLayout, ResourceTab } from '../components/resource-panel-layout'
import { InfiniteResourceList } from '@/components/InfiniteResourceList'
import LocalResourcePanel from '@/modules/video-editor/resource-plugin-system/components/local-resource-panel'
import { StickerItem, StickerLocalItem } from '../components/sticker-item'
import { useQueryClient } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { ResourceTabType } from '@/modules/video-editor/resource-plugin-system'

const StickerResourceTabs: ResourceTab[] = [
  {
    label: '贴纸库',
    value: ResourceTabType.ONLINE,
    showSearch: true,
    showCategorySelector: true,
    searchPlaceholder: '搜索贴纸，按回车键搜索'
  },
  {
    label: '我的贴纸',
    value: ResourceTabType.LOCAL,
    showSearch: true,
    showCategorySelector: false,
    searchPlaceholder: '搜索本地贴纸，按回车键搜索'
  }
]

/**
 * 贴纸面板组件
 * 显示各种贴纸模板，并允许用户将贴纸添加到时间轴
 */
function StickersPanel() {
  const queryClient = useQueryClient()
  const { data: pasterCategory } = useQueryPasterCategory()

  const [selectedCategory, setSelectedCategory] = useState<string>('')

  const [searchKey, setSearchKey] = useState<string>('')

  const infiniteQueryResult = useInfiniteQueryPasterUnified({
    pageSize: 30,
    selectedCategory: selectedCategory,
    keyword: searchKey,
  })

  //本地资源
  const { data: dirList } = useQueryPasterDirList() // 请求本地目录
  const [currentFolderId, setCurrentFolderId] = useState('') //当前目录

  //更新当前目录
  const onRefreshLocalResource = useCallback(async () => {
    await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.LOCAL_PASTER_FOLDER_LIST] })
  }, [queryClient])

  const handleFolderChange = useCallback(
    async (folderId: string) => {
      setCurrentFolderId(folderId)
      await onRefreshLocalResource()
    },
    [onRefreshLocalResource],
  )

  const handleCategoryChange = useCallback((value: string) => {
    setSelectedCategory(value)
  }, [])

  const renderStickerItem = useCallback(
    (item: PasterResource.Paster, index: number) => (
      <StickerItem
        key={`sticker-${item.id}-${index}`}
        sticker={item}
      />
    ),
    [],
  )

  const renderStickerContent = useCallback(() => (
    <InfiniteResourceList
      queryResult={infiniteQueryResult}
      renderItem={renderStickerItem}
      emptyText="该分类暂无贴纸"
      loadingText="加载贴纸中..."
      itemsContainerClassName="grid grid-cols-4 gap-3 pt-3 pb-3"
    />
  ), [infiniteQueryResult, renderStickerItem])

  const renderLocalStickerContent = useCallback(() => {
    const { data: localResources } = useInfiniteQueryLocalPasterList({
      pageSize: 30,
      folderUuid: currentFolderId,
      keyword: searchKey,
    })

    const handleUploadComplete = () => {
      void queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.LOCAL_PASTER_FOLDER_LIST] })
      void queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.LOCAL_PASTER_LIST] })
    }

    return (
      <LocalResourcePanel<PasterResource.PasterLocal>
        dirList={dirList || []}
        currentFolderId={currentFolderId}
        onFolderChange={handleFolderChange}
        resourceType={ResourceSource.LOCAL_STICK}
        resourceFolderType={ResourceSource.LOCAL_STICK_FOLDER}
        fileUploadTypes={['IMAGE']}
        searchKey={searchKey}
        resources={localResources}
        onUploadComplete={handleUploadComplete}
        emptyText="暂无本地贴纸"
        renderResourceItem={(resource, index) => (
          <StickerLocalItem
            key={`sticker-${resource.id}-${index}`}
            sticker={resource}
            isLocal={true}
          />
        )}
      />
    )
  }, [dirList, currentFolderId, handleFolderChange, searchKey])

  const hasStickers = useMemo(() => {
    const firstPage = infiniteQueryResult.data?.pages?.[0]
    return !!firstPage && firstPage.list.length > 0
  }, [infiniteQueryResult.data])

  // 配置标签页内容
  const tabs = useMemo(() => {
    return StickerResourceTabs.map(tab => ({
      ...tab,
      renderContent: () => {
        switch (tab.value) {
          case ResourceTabType.ONLINE:
            return renderStickerContent()
          case ResourceTabType.LOCAL:
            return renderLocalStickerContent()
          default:
            return null
        }
      },
      isEmpty: tab.value === ResourceTabType.ONLINE ? !hasStickers : false,
      emptyText: tab.value === ResourceTabType.ONLINE ? '该分类暂无贴纸' : '暂无本地贴纸',
    }))
  }, [renderStickerContent, renderLocalStickerContent, hasStickers])

  useEffect(() => {
    if (dirList?.length && !currentFolderId) {
      setCurrentFolderId(dirList[0].id)
    }
  }, [dirList, currentFolderId])

  return (
    <ResourcePanelLayout
      tabs={tabs}
      defaultTab={ResourceTabType.ONLINE}
      categories={pasterCategory}
      selectedCategory={selectedCategory}
      onCategoryChange={handleCategoryChange}
      searchKey={searchKey}
      onSearchChange={setSearchKey}
    />
  )
}

export default memo(StickersPanel)
