import React, { memo } from 'react'

import { TextOverlay } from '@app/shared/types/overlay'
import { DEFAULT_TEXT_OVERLAY } from '@/modules/video-editor/constants'

import StyledTextSelector from '@/modules/video-editor/components/common/styled-text-selector'
import { TextLayerRenderer } from '@clipnest/overlay-renderer'
import { EditorDraggableTypes, useTypedDraggable } from '@/modules/video-editor/components/editor-dnd-wrapper'
import { CloudResourceTypes } from '@/types/resources'

// 渲染默认文字预设项
const DefaultTextItem = () => {
  const defaultPreviewOverlay: TextOverlay = {
    ...DEFAULT_TEXT_OVERLAY,
    id: 0,
    content: '默认',
    width: 80,
    height: 80,
    styles: {
      ...DEFAULT_TEXT_OVERLAY.styles,
      fontSize: 28,
    }
  }

  const { setNodeRef, listeners, attributes } = useTypedDraggable(
    EditorDraggableTypes.Resource,
    'default-text',
    {
      resourceType: CloudResourceTypes.STYLED_TEXT,
    },
  )

  return (
    <div
      key="default-text"
      ref={setNodeRef}
      {...listeners}
      {...attributes}
      className="group relative overflow-hidden border bg-gray-200 dark:bg-background rounded border-white/10 transition-all dark:hover:border-white/20 hover:border-blue-500/80 cursor-pointer aspect-square w-20"
    >
      <div className="h-full w-full flex items-center justify-center rounded">
        <div className="text-base transform-gpu transition-transform group-hover:scale-102 dark:text-white text-gray-900/90 size-full flex justify-center items-center">
          <TextLayerRenderer overlay={defaultPreviewOverlay} />
        </div>
      </div>

      {/* Label */}
      <div className="absolute bottom-0 left-0 right-0 bg-black/60 text-white text-xs p-1 truncate">
        默认文字
      </div>
    </div>
  )
}

const TextPanel: React.FC = () => {
  return (
    <div className="h-full">

      {/* 花体字选择器 */}
      <StyledTextSelector headerContent={<DefaultTextItem />}  className="h-full" itemDraggable={true} />
    </div>
  )
}

export default memo(TextPanel)
