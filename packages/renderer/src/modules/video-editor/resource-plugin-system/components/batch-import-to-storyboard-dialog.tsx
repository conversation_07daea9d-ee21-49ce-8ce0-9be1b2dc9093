import React, { useState } from 'react'
import { MaterialResource } from '@/types/resources'
import {
  Overlay,
  OverlayType,
  SoundOverlay,
  StickerOverlay,
  StoryboardOverlay,
  VideoOverlay
} from '@app/shared/types/overlay'
import { PlayerDimensions, Track, TrackType } from '@/modules/video-editor/types'
import { DEFAULT_OVERLAY, FPS } from '@/modules/video-editor/constants'
import { generateNewOverlayId, getStoryboards } from '@/modules/video-editor/utils/track-helper'
import { cloneDeep } from 'lodash'
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'

interface BatchImportToStoryboardProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  resources: MaterialResource.Media[]
  tracks: Track[]
  playerDimensions: PlayerDimensions
  onImportComplete: (newTracks: Track[]) => void
}

interface StoryboardSelectDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  storyboards: StoryboardOverlay[]
  onConfirm: (selectedStoryboardIndex: number) => void
}

/**
 * 分镜选择弹窗组件
 * 用于批量导入素材时选择目标分镜
 */
function StoryboardSelectDialog({
  open,
  onOpenChange,
  storyboards,
  onConfirm
}: StoryboardSelectDialogProps) {
  const [selectedStoryboardIndex, setSelectedStoryboardIndex] = useState<number | null>(null)

  const handleConfirm = () => {
    if (selectedStoryboardIndex !== null) {
      onConfirm(selectedStoryboardIndex)
      onOpenChange(false)
      setSelectedStoryboardIndex(null)
    }
  }

  const handleCancel = () => {
    onOpenChange(false)
    setSelectedStoryboardIndex(null)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>选择目标分镜</DialogTitle>
        </DialogHeader>

        <div className="py-4">
          {storyboards.length === 0 ? (
            <div className="text-center text-gray-500 py-8">
              当前项目中没有分镜，请先创建分镜
            </div>
          ) : (
            <RadioGroup
              value={selectedStoryboardIndex?.toString()}
              onValueChange={value => setSelectedStoryboardIndex(parseInt(value))}
            >
              <div className="space-y-3 max-h-60 overflow-y-auto">
                {storyboards.map((storyboard, index) => (
                  <div key={storyboard.id} className="flex items-center space-x-2">
                    <RadioGroupItem value={index.toString()} id={`storyboard-${index}`} />
                    <Label
                      htmlFor={`storyboard-${index}`}
                      className="flex-1 cursor-pointer p-2 rounded hover:bg-gray-50"
                    >
                      <div className="font-medium">分镜 {index + 1}</div>
                      <div className="text-sm text-gray-500 truncate">
                        {storyboard.title || `分镜 ${index + 1}`}
                      </div>
                      <div className="text-xs text-gray-400">
                        时长: {Math.round(storyboard.durationInFrames / 30 * 10) / 10}秒
                      </div>
                    </Label>
                  </div>
                ))}
              </div>
            </RadioGroup>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleCancel}>
            取消
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={selectedStoryboardIndex === null || storyboards.length === 0}
          >
            确认导入
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

/**
 * 批量导入到分镜组件
 * 处理素材资源批量导入到指定分镜的逻辑
 */
export function BatchImportToStoryboardDialog({
  open,
  onOpenChange,
  resources,
  tracks,
  playerDimensions,
  onImportComplete
}: BatchImportToStoryboardProps) {
  const storyboards = getStoryboards(tracks)

  /**
   * 根据 MaterialResource.Media 创建对应的 Overlay
   */
  const createOverlayFromResource = (
    resource: MaterialResource.Media,
    storyboardIndex: number,
    overlayId: number
  ): Overlay | null => {
    // 将毫秒转换为帧数
    const durationInFrames = Math.round((resource.duration || 3000) / 1000 * FPS)

    const baseOverlay = {
      ...DEFAULT_OVERLAY,
      id: overlayId,
      durationInFrames,
      storyboardIndex,
      content: resource.fileName,
      src: resource.url || '',
      from: 0, // 将在插入时设置正确的起始帧
    }

    switch (resource.resType) {
      case MaterialResource.MediaType.VIDEO: {
        // VideoOverlay: 撑满播放器的宽度或高度（根据比例决定），左右居中+上下居中
        const { width: resourceWidth = playerDimensions.playerWidth, height: resourceHeight = playerDimensions.playerHeight } = resource
        const scaleX = playerDimensions.playerWidth / resourceWidth!
        const scaleY = playerDimensions.playerHeight / resourceHeight!
        const usingScale = Math.min(scaleX, scaleY)
        const width = Math.round(resourceWidth! * usingScale)
        const height = Math.round(resourceHeight! * usingScale)

        return {
          ...baseOverlay,
          type: OverlayType.VIDEO,
          width,
          height,
          left: (playerDimensions.playerWidth - width) / 2,
          top: (playerDimensions.playerHeight - height) / 2,
          originalDurationFrames: durationInFrames,
          originalMeta: {
            width: resourceWidth,
            height: resourceHeight,
          },
          styles: {
            objectFit: 'contain',
            volume: 1,
          }
        } satisfies VideoOverlay
      }

      case MaterialResource.MediaType.IMAGE: {
        // StickerOverlay: 使用播放器宽度的 1/4 作为宽度，高度根据原始比例计算
        const targetWidth = playerDimensions.playerWidth / 4
        const { width: resourceWidth = targetWidth, height: resourceHeight = targetWidth } = resource
        const aspectRatio = resourceHeight! / resourceWidth!
        const height = Math.round(targetWidth * aspectRatio)

        return {
          ...baseOverlay,
          type: OverlayType.STICKER,
          width: targetWidth,
          height,
          left: (playerDimensions.playerWidth - targetWidth) / 2,
          top: (playerDimensions.playerHeight - height) / 2,
          styles: {}
        } satisfies StickerOverlay
      }

      case MaterialResource.MediaType.AUDIO: {
        // SoundOverlay: 音频轨道
        return {
          ...baseOverlay,
          type: OverlayType.SOUND,
          width: playerDimensions.playerWidth,
          originalDurationFrames: durationInFrames,
          height: 100, // 音频轨道固定高度
          left: 0,
          top: 0,
          styles: {
            volume: 1,
          }
        } satisfies SoundOverlay
      }

      default:
        console.warn(`不支持的资源类型: ${resource.resType}`)
        return null
    }
  }

  /**
   * 获取轨道类型对应的 TrackType
   */
  const getTrackTypeForOverlay = (overlayType: OverlayType): TrackType => {
    switch (overlayType) {
      case OverlayType.VIDEO:
        return TrackType.VIDEO
      case OverlayType.STICKER:
        return TrackType.IMAGE
      case OverlayType.SOUND:
        return TrackType.SOUND
      default:
        return TrackType.MIXED
    }
  }

  /**
   * 查找或创建适合的轨道
   */
  const findOrCreateTrack = (
    tracks: Track[],
    overlayType: OverlayType,
    storyboardIndex: number
  ): { tracks: Track[]; trackIndex: number } => {
    const targetTrackType = getTrackTypeForOverlay(overlayType)
    const clonedTracks = cloneDeep(tracks)

    // 查找空的轨道（在指定分镜中没有 Overlay）
    const emptyTrackIndex = clonedTracks.findIndex(track =>
      track.type === targetTrackType &&
      !track.isGlobalTrack &&
      !track.overlays.some(overlay => overlay.storyboardIndex === storyboardIndex)
    )

    if (emptyTrackIndex !== -1) {
      return { tracks: clonedTracks, trackIndex: emptyTrackIndex }
    }

    // 没有找到空轨道，创建新轨道
    const newTrack: Track = {
      type: targetTrackType,
      isGlobalTrack: false,
      overlays: []
    }

    // 找到同类型轨道的最后位置
    const lastTrackIndex = clonedTracks
      .map((track, index) => ({ track, index }))
      .filter(({ track }) => track.type === targetTrackType && !track.isGlobalTrack)
      .pop()?.index

    const insertIndex = lastTrackIndex !== undefined ? lastTrackIndex + 1 : clonedTracks.length
    clonedTracks.splice(insertIndex, 0, newTrack)

    return { tracks: clonedTracks, trackIndex: insertIndex }
  }

  /**
   * 处理确认导入
   */
  const handleConfirmImport = (selectedStoryboardIndex: number) => {
    if (resources.length === 0) return

    let updatedTracks = cloneDeep(tracks)
    let currentOverlayId = generateNewOverlayId(updatedTracks)

    // 获取目标分镜的起始帧
    const targetStoryboard = storyboards[selectedStoryboardIndex]
    if (!targetStoryboard) return

    // 为每个资源创建 Overlay 并插入到合适的轨道
    for (const resource of resources) {
      const overlay = createOverlayFromResource(resource, selectedStoryboardIndex, currentOverlayId)
      if (!overlay) continue

      // 查找或创建合适的轨道
      const { tracks: newTracks, trackIndex } = findOrCreateTrack(updatedTracks, overlay.type, selectedStoryboardIndex)
      updatedTracks = newTracks

      // 设置正确的起始帧位置（分镜的起始位置）
      overlay.from = targetStoryboard.from

      // 将 Overlay 添加到轨道中
      updatedTracks[trackIndex].overlays.push(overlay)
      currentOverlayId++
    }

    onImportComplete(updatedTracks)
  }

  return (
    <StoryboardSelectDialog
      open={open}
      onOpenChange={onOpenChange}
      storyboards={storyboards}
      onConfirm={handleConfirmImport}
    />
  )
}
