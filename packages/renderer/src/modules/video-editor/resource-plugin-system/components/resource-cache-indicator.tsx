import React from 'react'
import { ResourceCacheType } from '@app/shared/types/resource-cache.types'
import { Check, Loader2Icon, PlusIcon } from 'lucide-react'
import { cn } from '@/components/lib/utils'
import { useResourceLoadingStore } from '@/modules/video-editor/hooks/resource/useResourceLoadingStore'

export interface ResourceCacheIndicatorProps {
  /**
   * 资源类型
   */
  resourceType: ResourceCacheType

  /**
   * 资源URL
   */
  resourceUrl: string

  /**
   * 是否正在加载
   */
  isLoading?: boolean

  /**
   * 图标大小
   */
  size?: number

  /**
   * 自定义类名
   */
  className?: string

  isCached?: boolean

  isChecking?: boolean

  onDownloadAndAddToTimeline?: () => void
}

export function ResourceCacheIndicator({
  resourceType,
  resourceUrl,
  className,
  isChecking,
  size = 16,
  isCached = false,
  onDownloadAndAddToTimeline,
}: ResourceCacheIndicatorProps) {
  const { _generateKey } = useResourceLoadingStore()

  const loadingStates = useResourceLoadingStore(state => state.loadingStates)

  const isLoading = loadingStates.get(_generateKey(resourceUrl, resourceType))?.isLoading || false

  const renderIcon = () => {
    if ((isLoading || isChecking)) {
      return (
        <Loader2Icon
          className="animate-spin text-blue-500 cursor-pointer"
          style={{ width: size, height: size }}
        />
      )
    }

    if (isCached) {
      return (
        <Check
          className="text-green-500 cursor-pointer"
          style={{ width: size, height: size }}
        />
      )
    }

    if (onDownloadAndAddToTimeline) {
      return (
        <button
          onClick={e => {
            e.stopPropagation()
            onDownloadAndAddToTimeline()
          }}
        >
          <PlusIcon
            className="text-gray-400 hover:text-blue-500 cursor-pointer"
            style={{ width: size, height: size }}
          />
        </button>
      )
    }
  }

  return (
    <div
      className={cn(
        'absolute right-0 bottom-0 transition-opacity duration-200',
        isLoading ? 'opacity-100' : 'opacity-0 group-hover:opacity-100',
        'flex items-center justify-center bg-gray-900/80 rounded p-1 cursor-pointer text-gray-500 hover:bg-gray-900/50 transition-all',
        className
      )}
    >
      {renderIcon()}
    </div>
  )
}
