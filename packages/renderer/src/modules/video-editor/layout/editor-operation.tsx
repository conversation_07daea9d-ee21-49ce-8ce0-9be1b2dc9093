import React, { FC, useMemo } from 'react'
import { useCachedOverlaysContext, useEditorContext } from '@/modules/video-editor/contexts'
import { Overlay } from '@app/shared/types/overlay'
import { useOperationPanelStore } from '@/modules/video-editor/stores/useOperationPanelStore'

import { TextToSpeechPanel } from '../components/operation/tts-panel'
import { GlobalSetting } from '../components/operation/global-setting'
import { OverlayEditingPanel } from '../components/operation/overlay-editing-panel'

export const EditorOperation: FC = () => {
  const { selectedOverlay } = useEditorContext()
  const { overlays } = useCachedOverlaysContext()

  const localOverlay = useMemo<Overlay | null>(
    () => overlays.find(o => o.id === selectedOverlay?.id) || null,
    [overlays, selectedOverlay]
  )

  const { activePanel } = useOperationPanelStore()

  if (activePanel === 'text-to-speech') {
    return <TextToSpeechPanel />
  }

  if (localOverlay) {
    return <OverlayEditingPanel localOverlay={localOverlay} />
  }

  return (
    <div className="p-4 h-full overflow-y-auto">
      <GlobalSetting />
    </div>
  )
}
