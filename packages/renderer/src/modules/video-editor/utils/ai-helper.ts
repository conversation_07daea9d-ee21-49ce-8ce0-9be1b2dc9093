import { AiModule } from '@/libs/request/api/ai'
import { FPS, TEXT_TO_SPEECH_CONFIG } from '@/modules/video-editor/constants'
import { cacheManager } from '@/libs/cache/cache-manager'
import { ResourceCacheType } from '@app/shared/types/resource-cache.types'

export async function submitAndWaitForAiTask<TFunc extends AiModule.AiFunctions>(
  func: TFunc,
  params: AiModule.RequestParamsByFunction<TFunc>
): Promise<AiModule.TaskResultByFunction<TFunc>> {
  const [task_id, submitError] = await AiModule.endpoints
    .submitTask(func, params)
    .then(r => [r.task_id, null])
    .catch(e => [null, e])

  if (submitError) {
    throw submitError
  }

  const startTime = Date.now()

  const pollTaskStatus = async (): Promise<AiModule.TaskResultByFunction<TFunc>> => {
    return new Promise((resolve, reject) => {
      const pollInterval = setInterval(async () => {
        const [taskInfo, error] = await AiModule.endpoints
          .queryTaskStatus<TFunc>(task_id)
          .then(r => [r, null])
          .catch(e => [null, e])

        if (error) {
          clearInterval(pollInterval)
          return reject(error)
        }

        if (taskInfo.status === 'SUCCESS') {
          clearInterval(pollInterval)
          const data = taskInfo.result?.data

          return data
            ? resolve(data)
            : reject(new Error('任务状态为 SUCCESS 但未找到数据'))
        }

        if (taskInfo.status === 'FAILURE' || taskInfo.status === 'REVOKED') {
          clearInterval(pollInterval)
          return reject(new Error('语音生成失败'))
        }

        if (Date.now() - startTime > TEXT_TO_SPEECH_CONFIG.taskTimeout) {
          clearInterval(pollInterval)
          return reject(new Error('语音生成超时'))
        }
      }, TEXT_TO_SPEECH_CONFIG.pollInterval)
    })
  }

  const [taskResult, error] = await pollTaskStatus()
    .then(r => [r, null])
    .catch(e => [null, e])

  if (error) {
    throw error
  }

  return taskResult
}

type TTSResult = {
  audioUrl: string
  localAudioUrl: string
  durationInFrames: number
}

export async function textToSpeech(data: AiModule.TextToSpeechRequestParams): Promise<[TTSResult, null] | [null, Error]> {
  try {
    const taskResultData = await submitAndWaitForAiTask('synthesis', data)

    const { video_name: audioUrl } = taskResultData

    // 缓存音频文件到本地
    const localAudioUrl = await cacheManager.resource.cacheResource(ResourceCacheType.SOUND, audioUrl)

    // 获取音频时长
    const audioDurationInSeconds = await window.editor.getAudioDuration(localAudioUrl)
    const durationInFrames = Math.round(audioDurationInSeconds * FPS)

    return [
      { audioUrl, localAudioUrl, durationInFrames },
      null
    ]
  } catch (e) {
    return [null, e]
  }
}

export async function recognizeAudioAsText(audioUrl: string): Promise<[AiModule.RecognitionResult, null] | [null, Error]> {
  try {
    const taskResultData = await submitAndWaitForAiTask('recognition', {
      oss_url: audioUrl
    })

    return [taskResultData, null]
  } catch (e) {
    return [null, e]
  }
}
