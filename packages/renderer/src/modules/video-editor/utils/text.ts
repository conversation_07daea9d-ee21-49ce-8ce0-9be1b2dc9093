import { StyledTextResource } from '@/types/resources'
import { OverlayType, TextOverlay } from '@app/shared/types/overlay'
import { TEXT_DEFAULT_CLOUD_FONT_NAME, TEXT_DEFAULT_CLOUD_FONT_SRC } from '@/modules/video-editor/constants'
import { omit } from 'lodash'

function safeParseColor(value?: string, defaultValue = 'transparent') {
  if (!value) return defaultValue
  return CSS.supports('color', value) ? value : defaultValue
}

/**
 * 从花体字的配置构建出 TextOverlay
 * 支持传入 `options.baseOverlay` 作为基础
 * @param data 字体样式资源
 * @param options 可选配置项
 */
export function buildTextOverlay(
  data?: StyledTextResource.StyledText,
  options: {
    isPreview?: boolean
    width?: number
    height?: number
    left?: number
    top?: number
    textContent?: string
    fontSize?: number
    baseOverlay?: Partial<TextOverlay>
  } = {}
): TextOverlay {
  const content = data?.content
  const {
    isPreview = false,
    width = isPreview ? 80 : 300,
    height = isPreview ? 80 : 50,
    left = isPreview ? 0 : 100,
    top = isPreview ? 0 : 100,
    textContent = isPreview ? '花体' : '默认文字',
    baseOverlay = {}
  } = options

  // 基础样式默认值
  const baseStyles = {
    fontSize: options?.fontSize ?? (isPreview ? 28 : 150),
    fontWeight: 'normal' as const,
    fontFamily: baseOverlay.styles?.fontFamily || content?.fontName || TEXT_DEFAULT_CLOUD_FONT_NAME,
    fontStyle: content?.italic ? 'italic' as const : 'normal' as const,
    underlineEnabled: Boolean(content?.underline),
    textAlign: 'center' as const,
    zIndex: 20,
    color: safeParseColor(content?.textColor, '#ffffff'),
    backgroundColor: safeParseColor(content?.backgroundColor, 'transparent')
  }

  // 轮廓（stroke）相关默认值
  const strokeStyles = {
    strokeEnabled: Boolean(content?.borderWidth),
    strokeWidth: content?.borderWidth,
    strokeColor: safeParseColor(content?.borderColor),
  }

  // 阴影（shadow）相关默认值
  const shadowStyles = {
    shadowEnabled: Boolean(content?.shadowDistance),
    shadowDistance: content?.shadowDistance,
    shadowAngle: content?.shadowAngle,
    shadowBlur: content?.shadowBlur,
    shadowOpacity: content?.shadowColorAlpha,
    shadowColor: safeParseColor(content?.shadowColor),
  }

  // 气泡字相关
  const additionalStyles = {
    backgroundImage: undefined,
    bubbleTextRect: undefined,
  }

  return {
    ...omit(baseOverlay, 'localSrc'),
    id: baseOverlay.id || data?.id || Date.now(),
    type: OverlayType.TEXT,
    src: baseOverlay?.src || data?.content.fontPath || TEXT_DEFAULT_CLOUD_FONT_SRC,
    content: baseOverlay.content ?? textContent,
    left: baseOverlay.left ?? left,
    top: baseOverlay.top ?? top,
    width: baseOverlay.width ?? width,
    height: baseOverlay.height ?? height,
    durationInFrames: baseOverlay.durationInFrames ?? 90,
    from: baseOverlay.from ?? 0,
    rotation: baseOverlay.rotation ?? 0,
    styles: {
      ...baseOverlay.styles,
      ...baseStyles,
      ...strokeStyles,
      ...shadowStyles,
      ...additionalStyles,
    }
  } satisfies TextOverlay
}
