
/**
 * Gets the dimensions of an image from URL
 */
export const getImageRatio = (imageUrl: string): Promise<number | null> => {
  return new Promise(resolve => {
    const img = new Image()

    img.onload = () => {
      resolve(img.naturalWidth && img.naturalHeight 
        ? img.naturalWidth / img.naturalHeight 
        : null)
    }

    img.onerror = () => resolve(null)

    img.src = imageUrl
  })
}
