import React, { FC, useEffect, useState } from 'react'
import { FormSlider, SectionTitle } from '../common/form-components'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import { useOverlayEditing } from '@/modules/video-editor/contexts'
import { SoundOverlay } from '@app/shared/types/overlay'

const DEFAULT_GRAIN_MAX_VOLUME = 3

export const SoundControls: FC<{ className?: string }> = ({
  className,
}) => {
  const { localOverlay: overlay, updateEditingOverlay } = useOverlayEditing<SoundOverlay>()

  const [grainMode, setGrainMode] = useState<boolean>(false)

  useEffect(() => {
    if (!overlay.styles?.volume) return
    if (overlay.styles.volume > 1) {
      setGrainMode(true)
    }
  }, [overlay.styles?.volume])

  return (
    <div className={className}>
      <div className="flex justify-between items-center mb-2">
        <SectionTitle title="音量" />
        <Button
          size="sm"
          variant="outline"
          onClick={() => {
            return updateEditingOverlay({
              styles: {
                volume: overlay?.styles?.volume === 0 ? 1 : 0,
              },
            }, true)
          }}
        >
          {(overlay?.styles?.volume ?? 1) === 0 ? '解除静音' : '静音'}
        </Button>
      </div>

      <div className="flex w-full items-center gap-2">
        <FormSlider
          value={overlay?.styles?.volume ?? 1}
          onChange={(val, commit) => {
            return updateEditingOverlay({
              styles: {
                volume: val,
              },
            }, commit)
          }}
          max={grainMode ? DEFAULT_GRAIN_MAX_VOLUME : 1}
          min={0}
          step={0.1}
          showInput={false}
        />
        <span className="text-xs text-muted-foreground min-w-[40px] text-right">
          {Math.round((overlay?.styles?.volume ?? 1) * 100)}%
        </span>
      </div>

      <div className="gap-6 flex items-center mt-2">
        <div className="flex items-center gap-2">
          <Checkbox
            id="gain"
            checked={grainMode}
            onCheckedChange={v => {
              if (!v && overlay.styles?.volume && overlay.styles?.volume > 1) {
                updateEditingOverlay({
                  styles: {
                    volume: 1,
                  },
                }, true)
              }
              setGrainMode(Boolean(v))
            }}
          />
          <Label htmlFor="gain">音频增益</Label>
        </div>
      </div>
    </div>
  )
}
