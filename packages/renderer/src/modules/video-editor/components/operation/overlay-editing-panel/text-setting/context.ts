import React from 'react'
import { TextOverlay } from '@app/shared/types/overlay'
import { DeepPartial } from '@/types/utils'

type TextSettingContextValues = {
  textOverlay: TextOverlay
  requestUpdateText(
    update: DeepPartial<TextOverlay>,
    commit?: boolean,
  ): void
}

export const TextSettingContext = React.createContext<TextSettingContextValues>(null as any)

export const useTextSettingContext = () => React.useContext(TextSettingContext)
