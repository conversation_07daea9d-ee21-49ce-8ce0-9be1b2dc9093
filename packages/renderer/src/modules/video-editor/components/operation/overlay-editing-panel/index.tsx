import React, { FC, useCallback } from 'react'
import { Overlay, OverlayType } from '@app/shared/types/overlay'
import {
  OverlayEditingContext,
  OverlayEditingContextValues,
  useCachedOverlaysContext,
  useEditorContext
} from '@/modules/video-editor/contexts'
import { merge } from 'lodash'

import { SingleOverlayUpdatePayload } from '@/modules/video-editor/utils/track-helper'

import { TextSetting } from './text-setting'
import { SoundSetting } from './sound-setting'
import { VideoSetting } from './video-setting'
import { CaptionSetting } from './caption-setting'
import { ImageSetting } from './image-setting'
import { StoryboardSetting } from './storyboard-setting'

export const OverlayEditingPanel: React.FC<{ localOverlay: Overlay }> = ({ localOverlay }) => {
  const { updateOverlay } = useEditorContext()
  const { requestUpdate } = useCachedOverlaysContext()

  const updateEditingOverlay = useCallback<OverlayEditingContextValues<any>['updateEditingOverlay']>(
    (updater, commit = false) => {
      if (!localOverlay) return

      const updatedOverlay = (typeof updater === 'function') && localOverlay
        ? updater(localOverlay as any)
        : merge({}, localOverlay, updater) as SingleOverlayUpdatePayload

      requestUpdate(localOverlay.id, updatedOverlay)

      if (commit) {
        updateOverlay(localOverlay.id, () => updatedOverlay)
      }
    },
    [localOverlay]
  )

  const overlayTypeComponentMap: Partial<Record<OverlayType, FC>> = {
    [OverlayType.TEXT]: TextSetting,
    [OverlayType.SOUND]: SoundSetting,
    [OverlayType.VIDEO]: VideoSetting,
    [OverlayType.CAPTION]: CaptionSetting,
    [OverlayType.STICKER]: ImageSetting,
    [OverlayType.STORYBOARD]: StoryboardSetting,
  }

  const Component = overlayTypeComponentMap[localOverlay.type]

  return (
    <div className="p-4 h-full overflow-y-auto">
      <OverlayEditingContext
        value={{
          localOverlay,
          updateEditingOverlay
        }}
      >
        {Component && <Component />}
      </OverlayEditingContext>
    </div>
  )
}
