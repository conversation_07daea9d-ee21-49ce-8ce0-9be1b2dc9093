import React from 'react'
import { FormNumberInput, FormSlider, SectionTitle } from '@/modules/video-editor/components/common/form-components'
import { MediaPaddingControls } from '@/modules/video-editor/components/common/media-padding-controls'
import { MediaControls } from '@/modules/video-editor/components/common/media-controls'
import { useOverlayEditing } from '@/modules/video-editor/contexts'
import { VideoOverlay } from '@app/shared/types/overlay'
import { VideoFadeControls } from '../video-fade-control'
import { SoundControls } from '@/modules/video-editor/components/shared/sound-controls'

export function BasicSettings() {
  const { localOverlay: videoOverlay, updateEditingOverlay } = useOverlayEditing<VideoOverlay>()

  return (
    <div className="space-y-2 w-full">
      {/*基本布局*/}
      <div className="overlay-setting-card">
        <SectionTitle title="基本布局" />
        <div className="grid grid-cols-2 gap-3 mb-3">
          <FormNumberInput
            label="X"
            value={videoOverlay.left}
            onChange={val => updateEditingOverlay({ left: val }, true)}
            decimalPlaces={2}
          />
          <FormNumberInput
            label="Y"
            value={videoOverlay.top}
            onChange={val => updateEditingOverlay({ top: val }, true)}
            decimalPlaces={2}
          />
        </div>

        <FormSlider
          min={-180}
          max={180}
          step={1}
          value={videoOverlay.rotation}
          label="旋转(°)"
          onChange={(val, commit) => updateEditingOverlay({ rotation: val }, commit)}
        />
        <MediaPaddingControls />
      </div>

      {/* 基础 */}
      <div className="overlay-setting-card">
        <SectionTitle title="基础" />
        <FormSlider
          max={1}
          min={0}
          step={0.1}
          value={videoOverlay?.styles?.opacity ?? 1}
          onChange={(val, commit) => updateEditingOverlay({ styles: { opacity: val } }, commit)}
          label="不透明度"
        />
      </div>

      {/* 音量 */}
      <div className="overlay-setting-card">
        <SoundControls />
      </div>

      {/* 淡入淡出控制 */}
      <div className="overlay-setting-card">
        <VideoFadeControls
          overlay={videoOverlay}
          onOverlayChange={updateEditingOverlay}
        />
      </div>

      {/* 变速和时长控制 */}
      <MediaControls />
    </div>
  )
}
