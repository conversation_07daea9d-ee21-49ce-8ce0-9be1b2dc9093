import React from 'react'
import { BaseOverlay } from '@app/shared/types/overlay'
import { FadeControls } from '@/modules/video-editor/components/common/fade-controls'

export const VideoFadeControls: React.FC<{
  overlay: BaseOverlay
  onOverlayChange: (updates: Partial<{ fadeInDuration?: number; fadeOutDuration?: number }>, commit?: boolean) => void
  className?: string
}> = ({ overlay, onOverlayChange, className }) => {
  return (
    <FadeControls
      overlay={overlay}
      onOverlayChange={(updates, commit) => onOverlayChange(updates, commit)}
      maxFadeInDuration={10}
      maxFadeOutDuration={10}
      className={className}
    />
  )
}
