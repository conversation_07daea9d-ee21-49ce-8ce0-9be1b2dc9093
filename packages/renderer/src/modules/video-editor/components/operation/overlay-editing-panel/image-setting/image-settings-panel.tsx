import React from 'react'
import { StickerOverlay } from '@app/shared/types/overlay'
import { AnimationSettings } from '../../../shared/animation-preview'
import { animationTemplates } from '@clipnest/remotion-shared/constants'
import { useOverlayEditing } from '@/modules/video-editor/contexts'

export const ImageSettingsPanel: React.FC = () => {
  const { localOverlay: stickerOverlay, updateEditingOverlay } = useOverlayEditing<StickerOverlay>()

  const handleEnterAnimationSelect = (animationKey: string) => {
    updateEditingOverlay({
      styles: {
        animation: {
          ...stickerOverlay.styles.animation,
          enter: animationKey,
        },

      }
    }, true)
  }

  const handleExitAnimationSelect = (animationKey: string) => {
    updateEditingOverlay({
      styles: {
        animation: {
          ...stickerOverlay.styles.animation,
          exit: animationKey,
        },
      }
    }, true)
  }

  return (
    <div className="space-y-6">
      <AnimationSettings
        animations={animationTemplates}
        selectedEnterAnimation={stickerOverlay.styles.animation?.enter}
        selectedExitAnimation={stickerOverlay.styles.animation?.exit}
        onEnterAnimationSelect={handleEnterAnimationSelect}
        onExitAnimationSelect={handleExitAnimationSelect}
      />
    </div>
  )
}
