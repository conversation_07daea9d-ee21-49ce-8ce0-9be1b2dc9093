import React, { useCallback, useMemo } from 'react'
import { FormNumberInput, FormSlider, SectionTitle } from './form-components'
import { Overlay, OverlayType, Progressive } from '@app/shared/types/overlay'
import { FPS } from '../../constants'
import _ from 'lodash'
import { useEditorContext, useOverlayEditing } from '@/modules/video-editor/contexts'
import {
  calculateMinSpeedLimit,
  findOverlay,
  updateProgressiveOverlayDuration
} from '@/modules/video-editor/utils/overlay-helper'

type ProgressiveOverlay = Overlay & Progressive

/**
 * 媒体控制组件的 Props 接口
 */
interface MediaControlsProps {
  /**
   * 变速的最小值，默认为 0.25
   */
  minSpeed?: number

  /**
   * 变速的最大值，默认为 2
   */
  maxSpeed?: number

  /**
   * 时长的最小值（秒），默认为 0.1
   */
  minDuration?: number

  /**
   * 时长的步长（秒），默认为 0.1
   */
  durationStep?: number

  /**
   * 是否显示变速控制，默认为 true
   */
  showSpeedControl?: boolean

  /**
   * 是否显示时长控制，默认为 true
   */
  showDurationControl?: boolean
}

/**
 * 时长裁剪(去片头/去片尾)控制组件
 */
function CutControl() {
  const frameToSecond = (frame = 0) => _.round((frame ?? 0) / FPS, 1)

  const { localOverlay, updateEditingOverlay } = useOverlayEditing<ProgressiveOverlay>()

  const { trimStartMax, trimEndMax } = useMemo(() => {
    const minDuration = FPS * 0.5
    return {
      trimStartMax: localOverlay.originalDurationFrames - minDuration - (localOverlay.trimEndFrames ?? 0),
      trimEndMax: localOverlay.originalDurationFrames - minDuration - (localOverlay.trimStartFrames ?? 0)
    }
  }, [localOverlay])

  const handleTrim = useCallback((props: { start?: number, end?: number }) => {
    return updateEditingOverlay(overlay => {
      const trimStartFrames = (props.start !== undefined ? props.start * FPS : undefined) ?? overlay.trimStartFrames ?? 0
      const trimEndFrames = (props.end !== undefined ? props.end * FPS : undefined) ?? overlay.trimEndFrames ?? 0

      return updateProgressiveOverlayDuration({
        ...overlay,
        trimStartFrames,
        trimEndFrames,
      })
    })
  }, [updateEditingOverlay])

  return (
    <div className="overlay-setting-card">
      <SectionTitle
        title={`${localOverlay.type === OverlayType.VIDEO ? '视频' : '音频'}时长裁剪`}
        onReset={() => {
          updateEditingOverlay(o => ({
            ...o,
            trimStartFrames: 0,
            trimEndFrames: 0,
            durationInFrames: o.originalDurationFrames * (o.speed ?? 1)
          }), true)
        }}
      />
      <div className="grid grid-cols-2 gap-3">
        <FormNumberInput
          label="去片头"
          suffix="秒"
          value={frameToSecond(localOverlay.trimStartFrames)}
          onChange={value => handleTrim({ start: value })}
          onBlur={() => updateEditingOverlay({}, true)}
          min={0}
          max={frameToSecond(trimStartMax)}
          step={0.1}
          decimalPlaces={1}
          formatDisplay={true}
        />
        <FormNumberInput
          label="去片尾"
          suffix="秒"
          value={frameToSecond(localOverlay.trimEndFrames)}
          onChange={value => handleTrim({ end: value })}
          onBlur={() => updateEditingOverlay({}, true)}
          min={0}
          max={frameToSecond(trimEndMax)}
          step={0.1}
          decimalPlaces={1}
          formatDisplay={true}
        />
      </div>

      {/* 显示裁剪后的时长 */}
      {/*{Boolean(localOverlay.trimStartFrames || localOverlay.trimEndFrames) && (*/}
      <div className="flex flex-col gap-3 px-4">
        <div className="flex justify-between text-xs text-muted-foreground rounded-md">
          <span>原始时长:</span>
          <span className="font-medium">
            {frameToSecond(localOverlay.originalDurationFrames)}秒
          </span>
        </div>
        <div className="flex justify-between text-xs text-muted-foreground rounded-md">
          <span>裁剪后时长(该时长不受变速影响):</span>
          <span className="font-medium">
            {frameToSecond(localOverlay.originalDurationFrames - (localOverlay.trimStartFrames ?? 0) - (localOverlay.trimEndFrames ?? 0))}秒
          </span>
        </div>
      </div>
      {/*)}*/}
    </div>
  )
}

/**
 * 变速控制组件
 *
 * 提供媒体变速调节功能，支持重置和实时数值显示
 *
 */
function SpeedControl({
  minSpeed = 0.1,
  maxSpeed = 5,
}: Pick<MediaControlsProps, 'minSpeed' | 'maxSpeed'>) {
  const { localOverlay, updateEditingOverlay } = useOverlayEditing<ProgressiveOverlay>()
  const { tracks } = useEditorContext()

  /**
   * 计算基于重叠限制的最小变速值
   */
  const minSpeedOfCurrentOverlay = useMemo(() => {
    const target = findOverlay(tracks, localOverlay.id)
    if (!target) return minSpeed
    return calculateMinSpeedLimit(tracks, target as ProgressiveOverlay)
  }, [tracks, localOverlay.id])

  /**
   * 实际使用的最小变速值（取用户设置和计算值的较大者）
   */
  const effectiveMinSpeed = Math.max(minSpeed, minSpeedOfCurrentOverlay)

  /**
   * 变速处理函数
   */
  const handleSpeedChange = (newSpeed: number, commit?: boolean) => {
    // 限制变速值不能低于计算出的最小值
    const clampedSpeed = _.clamp(newSpeed, effectiveMinSpeed, maxSpeed)

    return updateEditingOverlay(
      () => updateProgressiveOverlayDuration({
        ...localOverlay,
        speed: clampedSpeed
      }),
      commit
    )
  }

  /**
   * 重置按钮处理函数
   * 如果 1.0x 低于最小限制，则重置到最小允许值
   */
  const handleReset = () => {
    const resetSpeed = Math.max(1, effectiveMinSpeed)
    handleSpeedChange(resetSpeed, true)
  }

  return (
    <div className="overlay-setting-card">
      <div className="flex items-center justify-between">
        <SectionTitle title={`变速 (${localOverlay?.speed?.toFixed(2) ?? 1}X)`} />
        <button
          onClick={handleReset}
          className={`text-xs px-2.5 py-1.5 rounded-md transition-colors ${
            (localOverlay?.speed ?? 1) !== 1
              ? 'bg-primary/20 text-primary hover:bg-primary/30'
              : 'text-muted-foreground '
          }`}
        >
          重置
        </button>
      </div>
      <FormSlider
        showInput={false}
        min={minSpeed}
        max={maxSpeed}
        step={0.1}
        value={localOverlay?.speed ?? 1}
        onChange={(val, commit) => handleSpeedChange(val, commit)}
      />
      <div className="flex justify-between text-xs text-muted-foreground">
        <span>{minSpeed}x</span>
        <span>{maxSpeed}x</span>
      </div>
      {effectiveMinSpeed > minSpeed && (
        <div className="text-xs text-amber-600 mt-1">
          受后续内容限制，最低变速为 {effectiveMinSpeed.toFixed(2)}x
        </div>
      )}
    </div>
  )
}

/**
 * 时长控制组件
 *
 * 提供媒体时长调节功能，支持实时数值显示
 *
 * @deprecated
 */
function DurationControl({
  minDuration = 0.1,
  durationStep = 0.1
}: Pick<MediaControlsProps, 'minDuration' | 'durationStep'>) {
  const { localOverlay, updateEditingOverlay } = useOverlayEditing<ProgressiveOverlay>()

  const maxDuration = localOverlay.originalDurationFrames / FPS

  return (
    <div className="overlay-setting-card">
      <div className="flex items-center justify-between">
        <SectionTitle title="时长" />
        <span className="text-xs font-medium text-muted-foreground px-2 py-1 rounded">
          {((localOverlay?.durationInFrames ?? 0) / FPS).toFixed(2)}秒
        </span>
      </div>
      <FormSlider
        showInput={false}
        min={minDuration}
        max={maxDuration}
        step={durationStep}
        value={localOverlay?.durationInFrames / FPS}
        onChange={(val, commit) => updateEditingOverlay({ durationInFrames: Math.round(val * FPS) }, commit)}
      />
    </div>
  )
}

/**
 * 媒体控制组件. 包含以下子功能:
 *   - 变速控制
 *   - 时长控制 (去片头/去片尾)
 *
 * @template T - 继承自 BaseOverlay 且具有 speed 属性的 overlay 类型
 * @param props - 组件属性
 * @returns 媒体控制 UI
 */
export function MediaControls({
  minDuration = 0.1,
  durationStep = 0.1,
  showSpeedControl = true,
  showDurationControl = false
}: MediaControlsProps) {
  return (
    <>
      <CutControl />

      {showSpeedControl && (
        <SpeedControl />
      )}

      {showDurationControl && (
        <DurationControl
          minDuration={minDuration}
          durationStep={durationStep}
        />
      )}
    </>
  )
}
