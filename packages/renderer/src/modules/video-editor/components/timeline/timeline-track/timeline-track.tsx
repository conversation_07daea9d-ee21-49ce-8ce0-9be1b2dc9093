import { OverlayType, TextOverlay } from '@app/shared/types/overlay'
import React, { useCallback, useMemo } from 'react'
import clsx from 'clsx'
import { StyleOnlyTimelineItem, TimelineItem } from '../timeline-item'
import { createTextOverlay, findLastOverlay, getOverlayTimeRange } from '@/modules/video-editor/utils/overlay-helper'
import { PIXELS_PER_FRAME, TEXT_DEFAULT_CLOUD_FONT_SRC } from '@/modules/video-editor/constants'
import { Button } from '@/components/ui/button'
import { Plus } from 'lucide-react'
import { getTrackTypeLabel, isOverlayAcceptableByTrack } from '@/modules/video-editor/utils/track-helper'
import { useOverlayHelper } from '@/modules/video-editor/hooks/helpers/useOverlayHelper'
import { GhostElement, Track, TrackType } from '../../../types'
import { useEditorContext, useEditorSidebar, useTimeline, useTimelineDnd } from '@/modules/video-editor/contexts'
import { TimelineTrackContextMenu } from './timeline-track-context-menu'
import { TimelineTrackContext } from './timeline-track-context'
import { EditorDroppableTypes, useTypedDroppable } from '../../editor-dnd-wrapper'
import { ResourcePlugins } from '@/modules/video-editor/resource-plugin-system'
import { cacheManager } from '@/libs/cache/cache-manager'

export interface TimelineTrackProps extends Track {
  trackIndex: number
}

const TrailingContainer: React.FC<TimelineTrackProps> = ({
  type, overlays, trackIndex, isGlobalTrack
}) => {
  const { zoomScale } = useTimeline()
  const { isDragging } = useTimelineDnd()
  const { appendOverlayToTrack } = useOverlayHelper()
  const { setActiveResourcePanel } = useEditorSidebar()
  const { durationInFrames, playerDimensions, getPlayerDimensions } = useEditorContext()

  // 计算添加按钮的位置
  const styles = useMemo(
    () => {
      if (overlays.length === 0) {
        // 空轨道时显示在起始位置
        return {
          left: 0,
          width: durationInFrames * 1.1 * PIXELS_PER_FRAME * zoomScale
        }
      }

      // 找到最后一个 overlay 的结束位置
      const [, lastOverlayEnd] = getOverlayTimeRange(findLastOverlay(overlays))

      return {
        left: lastOverlayEnd * PIXELS_PER_FRAME * zoomScale,
        width: (durationInFrames - lastOverlayEnd) * PIXELS_PER_FRAME * zoomScale,
      }
    },
    [overlays, zoomScale, durationInFrames]
  )

  // 处理添加内容按钮点击
  const handleAddOverlay = useCallback(
    async (e: React.MouseEvent) => {
      e.stopPropagation()

      if (type === TrackType.STORYBOARD) {
        return appendOverlayToTrack(trackIndex, {
          type: OverlayType.STORYBOARD,
        })
      }

      if (type === TrackType.TEXT && isGlobalTrack) {
        const font = await cacheManager.font.cacheFont(TEXT_DEFAULT_CLOUD_FONT_SRC)
        const { playerWidth, playerHeight } = getPlayerDimensions()

        const overlayWidth = playerWidth * 0.8
        const overlay: Partial<TextOverlay> = createTextOverlay(font, {
          width: overlayWidth,
          left: (playerWidth - overlayWidth) / 2,
          top: playerHeight / 2,
        })

        return appendOverlayToTrack(trackIndex, {
          ...overlay,
          type: OverlayType.TEXT,
        })
      }

      if (type === TrackType.SOUND) {
        return setActiveResourcePanel(ResourcePlugins.MUSIC)
      }

      if (type === TrackType.IMAGE) {
        return setActiveResourcePanel(ResourcePlugins.STICKER)
      }
    },
    [type, trackIndex, overlays, playerDimensions, getPlayerDimensions]
  )

  return (
    <div
      className={clsx(
        'absolute inset-y-0 flex items-center px-4',
        'group', // 当悬浮在这个区域时显示
        isDragging && 'hidden', // 拖拽时完全隐藏
      )}
      style={{
        left: styles.left,
        zIndex: 40,
        width: styles.width,
      }}
    >
      {
        (
          (isGlobalTrack && [TrackType.TEXT, TrackType.SOUND, TrackType.IMAGE].includes(type))
          || type === TrackType.STORYBOARD
        ) && (
          <Button
            size="sm"
            variant="ghost"
            className="opacity-0 group-hover:opacity-100 pointer-events-auto bg-white/90 dark:bg-gray-800/90 border border-gray-200 dark:border-gray-700 shadow-sm hover:bg-white dark:hover:bg-gray-800 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 transition-all duration-200"
            onClick={handleAddOverlay}
          >
            <Plus className="w-3 h-3 mr-1" />
            添加{getTrackTypeLabel(type)}
          </Button>
        )
      }
    </div>
  )
}

export const TimelineTrack: React.FC<TimelineTrackProps> = props => {
  const { type, overlays, trackIndex } = props

  const { selectedOverlay } = useEditorContext()
  const { zoomScale, layout: { getTrackHeight } } = useTimeline()
  const { draggingOverlay, landingPoint, mousePosition } = useTimelineDnd()

  // 设置轨道为可放置区域
  const { setNodeRef } = useTypedDroppable(
    EditorDroppableTypes.TimelineTrack,
    trackIndex,
    {
      track: {
        ...props,
        index: trackIndex
      }
    }
  )

  const renderGhostElement = (
    el: GhostElement | null, isMousePosition = false
  ) => (
    el && el.row === trackIndex && (
      <div
        className={clsx(
          'absolute inset-y-[0.9px] rounded-md border-2 bg-blue-100/30 dark:bg-gray-400/30 shadow-md border-green-500',
          (isMousePosition || el.invalid) && 'border-dashed',
          type === TrackType.NARRATION && {
            'h-[50%]': true,
            'top-[50%]': el.overlay?.type === OverlayType.SOUND,
          },
          el.invalid
            ? 'border-red-500/80'
            : isMousePosition
              ? 'dark:border-white/50 cursor-grabbing'
              : 'border-green-600',
        )}
        style={{
          left: el.from * PIXELS_PER_FRAME * zoomScale,
          width: el.durationInFrames * PIXELS_PER_FRAME * zoomScale,
          zIndex: 50,
        }}
      >
        {isMousePosition && el.overlay && (
          <StyleOnlyTimelineItem
            item={el.overlay}
            width={el.durationInFrames * PIXELS_PER_FRAME * zoomScale}
            className="w-full h-full absolute top-0 left-0 opacity-50"
          />
        )}
      </div>
    )
  )

  const renderItems = useCallback(() => {
    if (type === TrackType.NARRATION) {
      const textOverlays = overlays.filter(o => o.type === OverlayType.TEXT)
      const soundOverlays = overlays.filter(o => o.type === OverlayType.SOUND)
      return (
        <div className="h-full flex flex-col">
          <div className="flex-1 relative">
            {textOverlays.map(overlay => (
              <TimelineItem key={overlay.id} item={overlay} />
            ))}
          </div>
          <div className="flex-1 relative">
            {soundOverlays.map(overlay => (
              <TimelineItem key={overlay.id} item={overlay} />
            ))}
          </div>
        </div>
      )
    }

    return overlays.map(overlay => (
      <TimelineItem key={overlay.id} item={overlay} />
    ))
  }, [overlays, type])

  return (
    <TimelineTrackContext.Provider value={{ currentTrack: { ...props, index: trackIndex } }}>
      <TimelineTrackContextMenu>
        <div
          ref={setNodeRef}
          style={{
            height: getTrackHeight(trackIndex)
          }}
          className={clsx(
            `bg-slate-100/90 dark:bg-gray-800 relative
           transition-all duration-200 ease-in-out select-none
           hover:bg-slate-200/90 dark:hover:bg-gray-700/90`,
            draggingOverlay && (
              !isOverlayAcceptableByTrack(draggingOverlay, { type })
              && 'hover:cursor-not-allowed'
            ),
            {
              'shadow-sm': selectedOverlay && overlays.length,
            },
          )}
        >
          {renderItems()}

          {/* Ghost element with updated colors - 现在同时支持 TimelineItem 和 Resource 拖拽 */}
          {renderGhostElement(landingPoint)}
          {renderGhostElement(mousePosition, true)}

          <TrailingContainer {...props} />
        </div>
      </TimelineTrackContextMenu>
    </TimelineTrackContext.Provider>
  )
}
