import React, { useMemo } from 'react'
import { useCurrentScale } from 'remotion'
import { Overlay, OverlayType } from '@app/shared/types/overlay'
import { useDraggable } from '@dnd-kit/core'
import { LAYER_CONTROL_DRAG_ACTIONS } from './constants'

const HANDLE_SIZE = 8

type ResizeHandleProps = {
  type: 'center-right'
  overlay: Overlay
}

export const ResizeHandle: React.FC<ResizeHandleProps> = ({ type, overlay }) => {
  const scale = useCurrentScale()
  const size = useMemo(() => Math.round(HANDLE_SIZE / scale), [scale])
  const borderSize = useMemo(() => 1 / scale, [scale])

  const sizeStyle = useMemo((): React.CSSProperties => ({
    position: 'absolute',
    height: Number.isFinite(size) ? size : HANDLE_SIZE,
    width: Number.isFinite(size) ? size : HANDLE_SIZE,
    backgroundColor: 'white',
    border: `${borderSize}px solid #7EE4D2`,
    zIndex: 9999,
    pointerEvents: 'all'
  }), [borderSize, size])

  const style: React.CSSProperties = useMemo(
    () => {
      const margin = -size / 2 - borderSize

      return {
        ...sizeStyle,
        marginBottom: margin,
        marginRight: margin,
        right: 0,
        bottom: overlay.height / 2,
        cursor: ' w-resize',
      }
    },
    [sizeStyle, overlay, type]
  )

  const { setNodeRef, listeners, attributes } = useDraggable({
    id: `layer-resize-${type}-${overlay.id}`,
    data: {
      action: LAYER_CONTROL_DRAG_ACTIONS.resize,
      overlay
    }
  })

  if (overlay.type === OverlayType.SOUND) {
    return null
  }

  return (
    <div
      ref={setNodeRef}
      {...listeners}
      {...attributes}
      style={style}
    />
  )
}
