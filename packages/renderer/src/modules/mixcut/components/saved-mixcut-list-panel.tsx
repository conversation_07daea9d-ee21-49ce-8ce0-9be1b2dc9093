import React from 'react'
import { Mixcut } from '@/types/mixcut'
import { MultiSelectableCard } from './multi-selectable-card'
import { clsx } from 'clsx'
import { useMixcutContext } from '../context/context'
import { formatRepetitionRate } from '@app/shared/utils'
import { useQuery } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { cacheManager } from '@/libs/cache/cache-manager'
import { VideoPreviewFrame } from '@/modules/mixcut/components/video-preview-frame'
import { findFirstVideoOverlay } from '@/modules/mixcut/utils'

const SavedMixcutCard: React.FC<Mixcut.SavedMixcut & { index: number }> = ({ index: i, ...mixcut }) => {
  const { saved } = useMixcutContext()

  const { data: mixcutData } = useQuery(({
    queryKey: [QUERY_KEYS.SAVED_MIXCUT_DETAIL, mixcut.id],
    queryFn: async () => {
      return cacheManager.mixcut.getMixcutData(mixcut.id)
    }
  }))

  if (!mixcutData) return null

  return (
    <MultiSelectableCard {...saved} index={i}>
      <div
        className={clsx(
          'w-48 h-64 relative outline-3 cursor-pointer rounded',
        )}
      >
        {/* 预览图片背景 */}
        <VideoPreviewFrame overlay={findFirstVideoOverlay(mixcutData.inputProps.overlays)} />

        {/* 重复率标签 */}
        <div className="absolute right-1 top-1 bg-black/70 text-white p-1 text-xs rounded">
          重复率{formatRepetitionRate(mixcut.repetitionRate)}
        </div>
      </div>
    </MultiSelectableCard>
  )
}

export const SavedMixcutListPanel = () => {
  const { list } = useMixcutContext().saved

  return (
    <div className="flex-1 h-fit flex flex-wrap gap-x-4 gap-y-6 p-4 overflow-y-auto">
      {list?.sort((a, b) => a.repetitionRate - b.repetitionRate)
        .map((combo, i) => (
          <SavedMixcutCard
            {...combo}
            key={i}
            index={i}
          />
        ))}
    </div>
  )
}
