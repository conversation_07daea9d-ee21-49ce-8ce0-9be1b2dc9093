import { useVirtualTab } from '@/contexts'
import { useQueryClient } from '@tanstack/react-query'
import React, { useCallback } from 'react'
import { toast } from 'react-toastify'
import { EditorModule } from '@/libs/request/api/editor'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { WithConfirm } from '@/components/WithConfirm'
import { Button } from '@/components/ui/button'
import { Trash2 } from 'lucide-react'
import { useMixcutContext } from '../context/context'
import { cacheManager } from '@/libs/cache/cache-manager'

export const SavedMixcutDeleteButton = () => {
  const { saved } = useMixcutContext()
  const { params } = useVirtualTab('Mixcut')
  const { scriptId } = params
  const queryClient = useQueryClient()

  const handleDeleteSelected = useCallback(async () => {
    const selectedIndicesArray = Array.from(saved.selectedIndices)
    if (selectedIndicesArray.length === 0) {
      toast.warning('请先选择要删除的混剪')
      return
    }

    if (!saved.list) {
      toast.error('获取混剪列表失败')
      return
    }

    try {
      // 获取选中混剪的 ID
      const selectedMixcuts = selectedIndicesArray.map(index => saved.list[index])
      const selectedIds = selectedMixcuts.map(mixcut => mixcut.id)

      // 调用删除接口
      await EditorModule.deleteSavedMixcut({ ids: selectedIds })

      // 刷新列表
      void queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.SAVED_MIXCUT_LIST, scriptId] })
      void cacheManager.mixcut.clearMixcuts(...selectedIds)

      // 清空选择
      saved.clearSelection()

      toast.success(`成功删除 ${selectedIds.length} 个混剪`)
    } catch (error) {
      console.error('删除混剪失败:', error)
      toast.error('删除混剪失败，请重试')
    }
  }, [saved, scriptId, queryClient])

  if (!saved.selectedIndices.size) return null

  return (
    <WithConfirm
      title="删除混剪"
      description={`确定要删除选中的 ${saved.selectedIndices.size} 个混剪吗？此操作不可恢复。`}
      confirmText="删除"
      confirmVariant="destructive"
      onConfirm={handleDeleteSelected}
    >
      <Button
        variant="destructive"
        size="sm"
      >
        <Trash2 className="w-4 h-4 mr-2" />
        删除所选混剪 ({saved.selectedIndices.size})
      </Button>
    </WithConfirm>
  )
}
