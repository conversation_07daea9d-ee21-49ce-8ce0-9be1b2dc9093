import React, { <PERSON>psW<PERSON><PERSON>hildren, useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { Drawer, DrawerClose, Drawer<PERSON>ontent, Drawer<PERSON>eader, <PERSON>er<PERSON><PERSON><PERSON>, DrawerTrigger } from '@/components/ui/drawer'
import { Button } from '@/components/ui/button'
import { NumberInput } from '@/components/ui/number-input'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import { X } from 'lucide-react'
import { useMixcutContext } from '@/modules/mixcut/context/context'
import { MIXCUT_PIPELINES } from '@app/shared/types/mixcut'

const VideoTransform: React.FC = () => {
  const { generation: { rulesForm: { watch, setValue } } } = useMixcutContext()

  const formValue = watch()

  const itemsMap = {
    [MIXCUT_PIPELINES.video.rotation]: '视频旋转',
    [MIXCUT_PIPELINES.video.scale]: '视频缩放',
    [MIXCUT_PIPELINES.video.positionOffset]: '视频位置偏移',
    [MIXCUT_PIPELINES.video.flip]: '视频翻转',
    [MIXCUT_PIPELINES.video.smartClip]: '智能截取',
    [MIXCUT_PIPELINES.video.speed]: '变速',
    [MIXCUT_PIPELINES.video.trimEnd]: '去片尾',
    [MIXCUT_PIPELINES.video.masking]: '视频遮罩',
  }

  const handleCheckboxChange = (pipelineKey: keyof typeof MIXCUT_PIPELINES.video, checked: boolean) => {
    console.log(`${pipelineKey} checked=${checked}`)
    setValue(`videoProcessing.status.${pipelineKey}`, checked)
  }

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        {Object.entries(itemsMap).map(([key, value]) => (
          <div key={key} className="flex space-x-2">
            <Checkbox
              id={`video-${key}`}
              checked={formValue.videoProcessing.status[key] || false}
              onCheckedChange={checked => handleCheckboxChange(key as any, !!checked)}
            />
            <Label htmlFor={`video-${key}`} className="text-sm font-normal cursor-pointer">
              {value}
            </Label>
          </div>
        ))}
      </div>
    </div>
  )
}

// 混剪规则 Tab 组件
const MixcutRulesTabs = () => {
  const tabs: Array<{
    value: string;
    label: string;
    component?: React.FunctionComponent<{}>
  }> = [
    { value: 'video-transform', label: '视频变换处理', component: VideoTransform },
    // { value: 'material-settings', label: '混剪素材设置' },
    { value: 'basic-setting', label: '基础规则设置' },
    // { value: 'background-music', label: '背景音乐设置' },
    // { value: 'subtitle-style', label: '随机字幕样式设置' },
    // { value: 'text-group-style', label: '随机文字组样式设置' },
    // { value: 'voice-style', label: '随机口播音色设置' },
    // { value: 'background-style', label: '随机背景设置' },
    // { value: 'effect-style', label: '随机特效设置' },
  ]

  const [activeTab, setActiveTab] = useState(tabs[0].value)

  return (
    <div className="flex h-full">
      {/* 左侧垂直 Tab 导航 */}
      <div className="w-48 border-r border-border bg-muted/30">
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          orientation="vertical"
          className="h-full"
        >
          <TabsList className="flex flex-col h-full w-full justify-start bg-transparent p-2 space-y-1">
            {tabs.map(tab => (
              <TabsTrigger
                key={tab.value}
                value={tab.value}
                className="w-full justify-start text-left px-3 py-2 text-sm font-normal
                  data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm
                  hover:bg-background/50 transition-colors"
              >
                {tab.label}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>
      </div>

      {/* 垂直分割线 */}
      <Separator orientation="vertical" className="h-full" />

      {/* 右侧内容区域 */}
      <div className="flex-1 p-6">
        <Tabs value={activeTab} className="h-full">
          {tabs.map(tab => (
            <TabsContent key={tab.value} value={tab.value} className="h-full">
              <div className="flex items-center justify-center h-full text-muted-foreground">
                {tab.component && React.createElement(tab.component)}
                {/*{tab.label} 内容区域*/}
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </div>
  )
}

// 混剪规则 Drawer 组件
export const MixcutRulesDrawer: React.FC<PropsWithChildren> = ({ children }) => {
  const {
    generation: {
      generateCount, drawerOpen,
      setGenerateCount, generateCombinations, setDrawerOpen
    }
  } = useMixcutContext()

  return (
    <Drawer direction="right" open={drawerOpen} onOpenChange={setDrawerOpen}>
      <DrawerTrigger asChild>
        {children}
      </DrawerTrigger>

      <DrawerContent className="max-w-[640px] p-0 flex flex-col">
        {/* Drawer 标题栏 */}
        <DrawerHeader className="px-6 py-4 border-b border-border">
          <div className="flex items-center justify-between">
            <DrawerTitle className="text-lg font-medium">
              混剪规则设置
            </DrawerTitle>
            <DrawerClose asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <X className="h-4 w-4" />
              </Button>
            </DrawerClose>
          </div>
        </DrawerHeader>

        {/* Drawer 内容区域 */}
        <div className="flex-1 overflow-hidden">
          <MixcutRulesTabs />
        </div>

        {/* 底部生成数量控制 */}
        <div className="px-6 py-4 border-t border-border bg-muted/30">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <span className="text-sm font-medium text-foreground">生成混剪视频数量:</span>
              <NumberInput
                value={generateCount}
                onChange={setGenerateCount}
                min={1}
                max={50}
                step={1}
                className="w-20"
              />
              <span className="text-xs text-muted-foreground">个</span>
            </div>
            <div className="text-xs text-muted-foreground">
              建议设置1-20个，数量过多可能影响生成速度
            </div>
          </div>

          <div className="flex justify-end">
            <Button variant="default" size="sm" onClick={generateCombinations}>
              生成混剪预览
            </Button>
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  )
}
