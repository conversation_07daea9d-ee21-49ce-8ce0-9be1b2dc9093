import { useQuery } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { EditorModule } from '@/libs/request/api/editor'
import { cacheManager } from '@/libs/cache/cache-manager'

export const useQuerySavedMixcuts = (scriptId: string) => {
  return useQuery({
    queryKey: [QUERY_KEYS.SAVED_MIXCUT_LIST, scriptId],
    queryFn: async () => {
      const { list } = await EditorModule.listMixcuts(scriptId)

      // 异步缓存混剪数据，不阻塞主流程
      cacheManager.mixcut.cacheMixcut(...list).catch(error => {
        console.error('缓存混剪数据失败:', error)
      })

      return list
    }
  })
}
