import { useForm, UseFormReturn } from 'react-hook-form'
import { MIXCUT_PIPELINES } from '@app/shared/types/mixcut'

export type MixcutRulesFormValue = {
  videoProcessing: {
    enabled: boolean
    status: Partial<Record<keyof typeof MIXCUT_PIPELINES.video, boolean>>
  }
}

export type MixcutRulesForm = UseFormReturn<MixcutRulesFormValue>

export const useMixcutRulesForm = () => {
  return useForm<MixcutRulesFormValue>({
    defaultValues: {
      videoProcessing: {
        enabled: false,
        status: {}
      }
    }
  })
}
