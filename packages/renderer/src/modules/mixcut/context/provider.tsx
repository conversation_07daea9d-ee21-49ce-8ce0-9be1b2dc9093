import React, { PropsWithChildren, useMemo, useState } from 'react'
import { calculateRenderableOverlays } from '@/modules/video-editor/shared'
import { EditorState } from '@/libs/cache/parts/editor.cache'
import { useVirtualTab } from '@/contexts'
import { MixcutContext, MixcutPageTabs } from './context'
import { TrackType } from '@/modules/video-editor/types'
import { useMultiSelection } from './useMultiSelection'
import { useQuerySavedMixcuts } from '@/modules/mixcut/hooks/useQuerySavedMixcuts'
import { useGenerationPart } from '@/modules/mixcut/context/useGenerationPart'

const useSavedPart = (_state: EditorState, scriptId: string) => {
  const { data } = useQuerySavedMixcuts(scriptId)

  const multiSelection = useMultiSelection(data || [])

  return {
    ...multiSelection,
  }
}

export const MixcutProvider: React.FC<PropsWithChildren<{ state: EditorState, defaultTab?: MixcutPageTabs }>> = ({
  children, state, defaultTab
}) => {
  const { params } = useVirtualTab('Mixcut')
  const scriptId = params?.scriptId

  const [activeTab, setActiveTab] = useState<MixcutPageTabs>(defaultTab || 'generation')

  const generationPart = useGenerationPart(state, scriptId)
  const savedPart = useSavedPart(state, scriptId)

  const playerOverlays = useMemo(() => {
    if (activeTab === 'generation' && generationPart.activeItem) {
      return calculateRenderableOverlays(state.tracks, {
        [TrackType.VIDEO]: generationPart.activeItem.videoCombo.selections,
        [TrackType.NARRATION]: generationPart.activeItem.narrationSelections,
      })
    }

    return []
  }, [activeTab, state.tracks, generationPart.activeItem, savedPart.activeItem])

  return (
    <MixcutContext.Provider
      value={{
        state,

        generation: generationPart,
        saved: savedPart,

        playerOverlays,
        activeTab,
        setActiveTab,
      }}
    >
      {children}
    </MixcutContext.Provider>
  )
}
