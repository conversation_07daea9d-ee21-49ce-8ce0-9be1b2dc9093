import React, { useCallback, useMemo, useState } from 'react'

export type MultiSelection<TData = any> = {
  list: TData[]

  activeIndex: number | null
  setActiveIndex(v: number | null): void
  activeItem: TData | null

  selectedIndices: Set<number>
  setSelectedIndices: React.Dispatch<React.SetStateAction<Set<number>>>
  toggleSelection: (index: number) => void
  clearSelection: () => void
  selectAll: () => void
}

export function useMultiSelection<TData = any>(datasource: TData[]): MultiSelection<TData> {
  const [activeIndex, setActiveIndex] = useState<number | null>(null)
  const [selectedIndices, setSelectedIndices] = useState<Set<number>>(new Set())

  // 多选相关方法
  const toggleSelection = useCallback((index: number) => {
    setSelectedIndices(prev => {
      const newSet = new Set(prev)
      if (newSet.has(index)) {
        newSet.delete(index)
      } else {
        newSet.add(index)
      }
      return newSet
    })
  }, [])

  const clearSelection = useCallback(() => {
    setSelectedIndices(new Set())
  }, [])

  const selectAll = useCallback(() => {
    setSelectedIndices(new Set(datasource.map((_, index) => index)))
  }, [datasource])

  const activeItem = useMemo(
    () => activeIndex !== null ? datasource[activeIndex] : null,
    [activeIndex, datasource]
  )

  return {
    list: datasource,
    activeIndex,
    setActiveIndex,
    activeItem,

    selectedIndices,
    setSelectedIndices,
    toggleSelection,
    clearSelection,
    selectAll,
  }
}
