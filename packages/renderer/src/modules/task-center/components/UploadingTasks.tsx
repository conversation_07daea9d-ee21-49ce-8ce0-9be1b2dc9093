import { Progress } from '@/components/ui/progress'
import { useTaskCenter } from '@/modules/task-center/context/context'
import { ResourceTypes } from '@app/shared/types/resource-cache.types'
import { UploadTask } from '@app/shared/types/local-db/entities/upload-task.entity'
import { UploadCloudIcon } from 'lucide-react'
import React, { FC, useMemo } from 'react'

export const UploadingTasks: FC<{ library: ResourceTypes }> = ({  library }) => {
  const { tasks } = useTaskCenter().uploading

  const uploadingTasks = useMemo(() => {
    return tasks
      .filter(task => task.library === library)
      .filter(t => t.status === UploadTask.Status.PENDING || t.status === UploadTask.Status.UPLOADING)
  }, [tasks])

  return (
    <div className="flex gap-4">
      {
        uploadingTasks?.map(task => (
          <div className="h-50 w-50 border border-input rounded bg-neutral-900/50 flex justify-center items-center flex-col gap-5 px-6 py-3">
            <UploadCloudIcon />
            <Progress value={Math.round(task.progress) * 100} />
          </div>
        ))
      }
    </div>
  )
}
