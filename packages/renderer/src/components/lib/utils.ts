import { clsx, type ClassValue } from 'clsx'
import { twMerge } from 'tailwind-merge'
import dayjs from 'dayjs'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * 格式化时间戳为可读时间
 * @param timestamp 时间戳（毫秒）
 * @param format 时间格式，默认为 'YYYY-MM-DD HH:mm'
 * @returns 格式化后的时间字符串，如果时间戳无效则返回 '-'
 */
export function formatTimestamp(timestamp?: number, format: string = 'YYYY-MM-DD HH:mm'): string {
  if (!timestamp) return '-'
  return dayjs(timestamp).format(format)
}

/**
 * 计算目标时间与当前时间的剩余时间
 * @param timestamp 目标时间戳（毫秒）
 * @param units 指定需要返回的单位（默认 ['day','hour','minute','second']）
 * @param options 配置项 { hideZero: 是否隐藏 0 值，默认 true }
 * @returns 格式化后的剩余时间字符串，例如 "2天3小时15分20秒"
 */
export function formatCountdown(
  timestamp: number,
  units: Array<'day' | 'hour' | 'minute' | 'second'> = ['day', 'hour', 'minute', 'second'],
  options: { hideZero?: boolean } = { hideZero: true }
): string {
  if (!timestamp) return '-'

  const now = Date.now()
  let diff = Math.max(timestamp - now, 0) // 防止负数

  const day = Math.floor(diff / (1000 * 60 * 60 * 24))
  diff %= 1000 * 60 * 60 * 24

  const hour = Math.floor(diff / (1000 * 60 * 60))
  diff %= 1000 * 60 * 60

  const minute = Math.floor(diff / (1000 * 60))
  diff %= 1000 * 60

  const second = Math.floor(diff / 1000)

  const parts: string[] = []
  const { hideZero = true } = options

  const pushPart = (val: number, label: string, unit: 'day' | 'hour' | 'minute' | 'second') => {
    if (!units.includes(unit)) return
    if (hideZero && val === 0) return
    parts.push(`${val}${label}`)
  }

  pushPart(day, '天', 'day')
  pushPart(hour, '小时', 'hour')
  pushPart(minute, '分', 'minute')
  pushPart(second, '秒', 'second')

  return parts.length > 0 ? parts.join('') : '0秒'
}
