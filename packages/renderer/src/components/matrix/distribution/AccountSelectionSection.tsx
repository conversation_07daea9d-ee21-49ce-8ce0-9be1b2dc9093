import React, { useRef } from 'react'
import { Users, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { SelectAccountDialog, SelectAccountDialogRef } from '@/components/matrix/SelectAccountDialog'
import { Account } from '@/types/matrix/douyin'

interface AccountSelectionSectionProps {
  selectedAccounts: Account[]
  onAccountSelect: (accounts: Account[]) => void
  onRemoveAccount: (accountId: number) => void
}

export const AccountSelectionSection: React.FC<AccountSelectionSectionProps> = ({
  selectedAccounts,
  onAccountSelect,
  onRemoveAccount
}) => {
  const selectAccountDialogRef = useRef<SelectAccountDialogRef>(null)

  return (
    <div className="space-y-4">
      <div className="space-y-3">
        <Label className="flex items-center gap-2">
          <Users className="w-4 h-4" />
          选择账号 <span className="text-red-500">*</span>
        </Label>
        <div className="border py-3 px-4 rounded flex flex-col gap-2">
          <div className="flex items-center gap-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => selectAccountDialogRef.current?.open({
                selectedAccountIds: selectedAccounts.map(account => account.id)
              })}
              className="flex items-center gap-2"
            >
              <Users className="w-4 h-4" />
              选择账号
              {selectedAccounts.length > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {selectedAccounts.length}
                </Badge>
              )}
            </Button>
            {selectedAccounts.length > 0 && (
              <span className="text-sm text-muted-foreground">
                发布账号个数： {selectedAccounts.length} 个
              </span>
            )}
          </div>
          {selectedAccounts.length > 0 && (
            <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
              {selectedAccounts.map(account => (
                <Badge
                  key={account.id}
                  variant="outline"
                  className="flex items-center gap-1 px-3 py-2"
                >
                  {account.nickname || account.orgNickname}
                  <X
                    className="w-3 h-3 cursor-pointer hover:text-red-500"
                    onClick={() => onRemoveAccount(account.id)}
                  />
                </Badge>
              ))}
            </div>
          )}
        </div>
        <div className="text-xs text-red-500">抖音发布账号数不能大于视频数</div>
      </div>

      {/* 账号选择对话框 */}
      <SelectAccountDialog
        ref={selectAccountDialogRef}
        onConfirm={onAccountSelect}
      />
    </div>
  )
}
