import React, { useRef, useState } from 'react'
import { FieldErrors } from 'react-hook-form'
import { Play, Plus, RotateCcw, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { isEmpty } from 'lodash'
import { cn } from '@/components/lib/utils'
import { VideoSelectDrawer, VideoSelectDrawerRef } from '../VideoSelectDrawer'
import { VideoPreviewPopover } from '../VideoPreviewModal'

import { UploadModule } from '@app/shared/types/ipc/file-uploader'
import {  AuthedImgByObjectId } from '@/components/authed-img'
import { uploadBufferViaIPC } from '@/libs/request/upload'
import { toast } from 'react-toastify'
import { Badge } from '@/components/ui/badge'

export interface SimpleVideo {
  url: string
  cover?: string
  customCover?: string
  orgCover?: string
  name?: string
}

interface VideoSelectionSectionProps {
  selectedVideos: SimpleVideo[]
  onVideoSelect: (videos: SimpleVideo[]) => void
  onRemoveVideo: (videoUrl: string) => void
  onUpdateVideoCover: (videoUrl: string, customCoverUrl: string) => void
  onReplaceVideo?: (oldVideoUrl: string, newVideo: SimpleVideo) => void
  errors?: FieldErrors
}

const CustomUploader: React.FC<{
  video: SimpleVideo,
  onComplete: (newCoverObjectId: string) => void
} > = ({
  video,
  onComplete
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleChange = async (file: File) => {
    const buffer = await file.arrayBuffer()
    const uuid = crypto.randomUUID().replace(/-/g, '')
    const suffix = file.type.split('/').at(-1)
    const filename = suffix ? `${uuid}.${suffix}` : uuid
    const result = await uploadBufferViaIPC(buffer, filename, uuid, undefined, UploadModule.script)
    if (!result.success || !result.objectId) {
      toast.error('图片上传失败：' + result.error)
      return
    }
      
    onComplete?.(result.objectId)
  }
  
  return (
    <VideoCard tagLabel="封面" className="relative overflow-hidden">
      <div className="w-full h-full overflow-hidden">
        {video.cover ? (
          <AuthedImgByObjectId
            src={video.cover}
            alt="视频封面"
            className="w-full h-full object-cover rounded-lg"
          />
          
        ) : (
          <div className="w-full h-full flex items-center justify-center text-xs text-neutral-400">
            无封面
          </div>
        )}
      </div>

      <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center gap-2">
        <Button
          size="sm"
          variant="secondary"
          className="text-xs px-3 py-1 h-auto bg-neutral-600 hover:bg-neutral-700 text-white border-0"
          type="button"
          onClick={() => fileInputRef.current?.click()}

        >
          点击上传
          
        </Button>
      </div>

      <div className="text-xs text-muted px-2 py-1 w-full bg-neutral-800/80">
        {video?.name ?? '暂无名称'}
      </div>

      <input
        ref={fileInputRef}
        className="hidden"
        type="file"
        accept="image/png,image/jpeg"
        onChange={async e => {
          const [file] = e.target.files ?? []
          if (!file) return
          await toast.promise(handleChange(file), {
            pending: '图片上传中...',
            success: '上传成功',
            error: '上传失败',
          })
          e.target.value = ''
        }}
      />
    </VideoCard>

  )
}

const VideoCard: React.FC<{
  className?: string
  children: React.ReactNode
  onClick?: () => void
  tagLabel: string
}> = ({ children, className, tagLabel, onClick, ...rest }) => {
  return (
    <div
      {...rest}
      onClick={onClick}
      className={cn(className,
        'border-2 border-dashed border-neutral-700 h-84 aspect-[9/16] bg-neutral-800/50 rounded-lg flex flex-col items-center justify-center cursor-pointer hover:border-blue-500/50 hover:bg-neutral-800/90 transition-all duration-200')}
    >
      <Badge variant="secondary" className="absolute left-2 top-2 ">{tagLabel ?? '-'}</Badge>
      {children}
    </div>
  )
}

export const VideoSelectionSection: React.FC<VideoSelectionSectionProps> = ({
  selectedVideos,
  onVideoSelect,
  onRemoveVideo,
  onUpdateVideoCover,
  onReplaceVideo,
  errors
}) => {
  const videoSelectDrawerRef = useRef<VideoSelectDrawerRef>(null)
  const [replacingVideoUrl, setReplacingVideoUrl] = useState<string | null>(null)

  const handleOpenVideoSelect = () => {
    videoSelectDrawerRef.current?.open(selectedVideos)
  }

  const handleReplaceVideo = (currentVideo: SimpleVideo) => {
    setReplacingVideoUrl(currentVideo.url)
    videoSelectDrawerRef.current?.open([], true, selectedVideos)
  }
  
  const handleVideoSelectResult = (videos: SimpleVideo[]) => {
    if (replacingVideoUrl && videos.length > 0) {
      const newVideo = videos[0]
      if (onReplaceVideo) {
        onReplaceVideo(replacingVideoUrl, newVideo)
      } else {
        const updatedVideos = selectedVideos.map(video =>
          video.url === replacingVideoUrl ? newVideo : video
        )
        onVideoSelect(updatedVideos)
      }
      setReplacingVideoUrl(null)
    } else {
      onVideoSelect(videos)
    }
  }

  return (
    <div className="space-y-2">
      <Label className="flex items-center gap-2">
        添加视频 <span className="text-red-500">*</span>
      </Label>
      <p className="text-sm text-muted-foreground mb-4">
        视频时长≤15分钟，大小≤4GB，推荐上传720p以上分辨率，支持常见视频格式，推荐使用MP4。
      </p>

      <div className="flex gap-2 flex-wrap ">
        {/* 已选择的视频列表 */}
        {selectedVideos.map(video => (
          <div key={video.url} className="relative group/container flex items-center gap-4 bg-neutral-700/50 w-fit p-3 rounded-2xl h-[360px]">
            {/* 删除按钮 */}
            <Button
              variant="ghost"
              size="sm"
              className="absolute -top-2 -right-2 z-20 h-6 w-6 p-0 bg-red-600 hover:bg-red-700 text-white rounded-full opacity-0 group-hover/container:opacity-100 transition-opacity duration-200"
              onClick={() => onRemoveVideo(video.url)}
            >
              <X className="w-3 h-3" />
            </Button>

            <div className="flex gap-2 items-center">
              {/* 第一个VideoCard - 视频预览卡片 */}
              <div className="relative group">
                <VideoCard tagLabel="视频" className="relative overflow-hidden">
                  <div className="w-full h-full overflow-hidden">
                    {video.orgCover ? (
                      <AuthedImgByObjectId
                        src={video.orgCover}
                        alt="视频文件"
                        className="w-full h-full object-cover rounded-lg"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-xs text-neutral-400">
                        无封面
                      </div>
                    )}
                  </div>

                  {/* 悬浮按钮层 */}
                  <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center gap-2">
                    <VideoPreviewPopover
                      video={video}
                      trigger={
                        <Button
                          size="sm"
                          variant="secondary"
                          className="text-xs px-3 py-1 h-auto bg-blue-600 hover:bg-blue-700 text-white border-0"
                        >
                          <Play className="w-3 h-3 mr-1" />
                          预览
                        </Button>
                      }
                    />
                    <Button
                      size="sm"
                      variant="secondary"
                      className="text-xs px-3 py-1 h-auto bg-neutral-600 hover:bg-neutral-700 text-white border-0"
                      onClick={() => handleReplaceVideo(video)}
                      type="button"

                    >
                      <RotateCcw className="w-3 h-3 mr-1" />
                      更换视频
                    </Button>
                  </div>

                  <div className="text-xs text-muted px-2 py-1 w-full bg-neutral-800/80">
                    {video?.name ?? '暂无名称'}
                  </div>
                </VideoCard>
              </div>

              <div className="relative group">
                <CustomUploader
                  video={video}
                  onComplete={customCoverObjectId => {
                    if (customCoverObjectId) {
                      console.log(`CUSTOM_UPLOADER: ${customCoverObjectId}`)
                      
                      onUpdateVideoCover(video.url, customCoverObjectId)
                    }
                  }}
                />

              </div>
            </div>
          </div>
        ))}

        {/* 继续选择视频按钮 - 只在已有视频时显示 */}
        <div className="flex items-center justify-center bg-neutral-700/30 border-2 border-dashed border-neutral-600 rounded-2xl px-6 h-[360px] ">
          <Button
            type="button"
            variant="outline"
            onClick={handleOpenVideoSelect}
            className="flex items-center gap-2 h-auto py-2 px-6 border-neutral-500 hover:border-blue-500 hover:bg-blue-500/10"
          >
            <Plus className="w-6 h-6" />
            <span className="text-sm">添加选择</span>
          </Button>
        </div>
      </div>

      {errors?.videoList && (
        <p className="text-sm text-red-500">{(errors.videoList as any).message}</p>
      )}

      {/* 视频选择抽屉 */}
      <VideoSelectDrawer
        ref={videoSelectDrawerRef}
        onVideoSelect={handleVideoSelectResult}
      />
    </div>
  )
}
