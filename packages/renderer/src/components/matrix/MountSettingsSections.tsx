import { MountType } from '@/types/matrix/douyin'
import { Label } from '@/components/ui/label'
import { Controller, useFormContext } from 'react-hook-form'
import React from 'react'
import { RadioGroup, RadioGroupItem } from '../ui/radio-group'
import { MatrixPublishFormData } from '@/pages/Matrix/publish'
import { toast } from 'react-toastify'
import { cn } from '../lib/utils'

const mountTypeOptions = [
  { value: MountType.NONE, label: '不挂载' },
  { value: MountType.LOCATION, label: '位置' },
  { value: MountType.CART, label: '购物车' }
]

export const MountSettingsSection = () => {
  const { control, watch, formState: { errors } } = useFormContext<MatrixPublishFormData>()

  const isCartDisabled = watch('accountIds')?.length === 0
    
  const handleCartOptionClick = (e: React.MouseEvent) => {
    if (isCartDisabled) {
      e.preventDefault()
      e.stopPropagation()
      toast('请先选择您的发布账号，完成选择后才能成功添加到购物车。', {
        type: 'warning'
      })
    }
  }
 
  return (
    <div className="space-y-6">
      {/* 视频位置/小程序挂载 */}
      <div className="flex gap-6 items-center">
        <Label>视频位置/小程序挂载</Label>
        <Controller
          name="mountType"
          control={control}
          render={({ field }) => (
            <RadioGroup
              value={field.value.toString()}
              onValueChange={(value: string) => {
                const numValue = Number(value)

                if (numValue === MountType.CART && isCartDisabled) {
                  return handleCartOptionClick({} as React.MouseEvent)
                }
                field.onChange(numValue)
              }}
              className="flex gap-3"
            >
              {mountTypeOptions.map(option => {
                const isCartOption = option.value === MountType.CART
                const isDisabled = isCartOption && isCartDisabled

                return (
                  <div key={option.value} className="flex items-center space-x-2">
                    <RadioGroupItem
                    
                      value={option.value.toString()}
                      id={`mountType-${option.value}`}
                      disabled={isDisabled}
                    />
                    <Label
                      htmlFor={`mountType-${option.value}`}
                      className={cn({ 'cursor-not-allowed opacity-50': isDisabled }, 'text-sm font-normal cursor-pointer ')}
                    >
                      {option.label}
                    </Label>
                  </div>
                )
              })}
            </RadioGroup>
          )}
        />
        {errors?.mountType && (
          <p className="text-sm text-red-500">{(errors.mountType).message}</p>
        )}
      </div>
     
    </div>
  )
}
