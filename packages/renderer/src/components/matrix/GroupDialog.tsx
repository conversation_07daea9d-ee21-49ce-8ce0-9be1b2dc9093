import React, { forwardRef, useImperativeHandle, useState } from 'react'
import { genForm } from '@/libs/tools/form'
import { useFormContext } from 'react-hook-form'
import { z } from 'zod'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle, } from '@/components/ui/dialog'
import { MatrixModule } from '@/libs/request/api/matrix'
import { useRequest } from '@/hooks/useRequest'
import { queryClient } from '@/main'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { CreateGroup, GroupItem, UpdateGroup } from '@/types/matrix/douyin'

// 表单验证schema
const groupFormSchema = z.object({
  name: z.string().min(1, '请输入分组名称').max(20, '分组名称不能超过20个字符'),
  tags: z.string().max(50, '备注不能超过50个字符').optional().default(''),
})

// 公共表单字段配置
const groupFormFields = {
  name: {
    label: '分组名称',
    render: ({ field }: any) => {
      const { setValue } = useFormContext()
      return (
        <div className="relative">
          <Input
            {...field}
            placeholder="请输入分组名称"
            className="pr-12"
            onChange={e => setValue('name', e.currentTarget.value.slice(0, 20))}
          />
          <span className="absolute right-3 top-1/2 -translate-y-1/2 text-xs text-muted-foreground">
            {field.value?.length || 0} /20
          </span>
        </div>
      )
    },
  },
  tags: {
    label: '备注',
    render: ({ field }: any) => {
      const { setValue } = useFormContext()
      return (
        <div className="relative">
          <Textarea
            {...field}
            placeholder="请输入备注"
            className="min-h-[80px] pr-12 resize-none"
            onChange={e => setValue('tags', e.currentTarget.value.slice(0, 50))}
          />
          <span className="absolute right-3 bottom-3 text-xs text-muted-foreground">
            {(field.value || '').length}/50
          </span>
        </div>
      )
    },
  },
}

// 统一的分组表单
const GroupForm = genForm(groupFormSchema, {
  fields: groupFormFields,
})

// 分组对话框组件接口
export interface GroupDialogRef {
  openCreateDialog: (parentId?: number) => void
  openEditDialog: (group: Partial<GroupItem>) => void
}

interface GroupDialogProps {
  onSuccess?: () => void
}

export const GroupDialog = forwardRef<GroupDialogRef, GroupDialogProps>(({ onSuccess }, ref) => {
  const [open, setOpen] = useState(false)
  const [editingGroup, setEditingGroup] = useState<Partial<GroupItem> | null>(null)
  const [parentId, setParentId] = useState<number | undefined>()

  const isEditing = !!editingGroup
  const title = isEditing ? '编辑分组' : '新建分组'
  const submitText = isEditing ? '保存' : '确定'

  const defaultValues = isEditing
    ? { name: editingGroup.name, tags: editingGroup.tags || '' }
    : { name: '', tags: '' }

  useImperativeHandle(ref, () => ({
    openCreateDialog: (parentGroupId?: number) => {
      setEditingGroup(null)
      setParentId(parentGroupId)
      setOpen(true)
    },
    openEditDialog: (group: Partial<GroupItem>) => {
      setEditingGroup(group)
      setParentId(undefined)
      setOpen(true)
    }
  }), [])

  const handleClose = () => {
    setOpen(false)
    setEditingGroup(null)
    setParentId(undefined)
  }

  const createGroupMutation = useRequest(
    (data: CreateGroup) => MatrixModule.createGroup(data),
    {
      actionName: '创建分组',
      onSuccess: () => {
        handleClose()
        onSuccess?.()
      }
    }
  )

  // 使用 useRequest 处理更新分组
  const updateGroupMutation = useRequest(
    (data: UpdateGroup) => MatrixModule.updateGroup(data),
    {
      actionName: '更新分组',
      onSuccess: () => {
        handleClose()
        onSuccess?.()
      }
    }
  )

  const handleSubmit = async (data: { name: string; tags: string }) => {
    if (isEditing && editingGroup.id) {
      // 编辑分组
      const updateData: UpdateGroup = {
        id: editingGroup.id,
        name: data.name,
        tags: data.tags || '',
      }
      await updateGroupMutation.mutateAsync(updateData)
    } else {
      // 创建分组
      const createData: CreateGroup = {
        name: data.name,
        tags: data.tags || '',
        parentId: parentId || undefined
      }
      await  createGroupMutation.mutateAsync(createData)
    }

    queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ACCOUNT_GROUP] })
  }

  const pending = createGroupMutation.isPending || updateGroupMutation.isPending

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>

        <GroupForm
          defaultValues={defaultValues}
          onSubmit={handleSubmit}
        >
          <DialogFooter className="gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={pending}
            >
              取消
            </Button>
            <Button type="submit" disabled={pending}>
              {pending ? '提交中...' : submitText}
            </Button>
          </DialogFooter>
        </GroupForm>
      </DialogContent>
    </Dialog>
  )
})
