import { TileImageInfo } from '@/hooks/material/useLoadTileImageInfo'
import React, { useMemo, useState } from 'react'

export type TileImageProps = {
  tileInfo: TileImageInfo
  aspectRatio: number
  currentFrame: number
}

// 瓦片图中每一行最多展示 10 个关键帧
const MAX_FRAMES_PER_ROW = 10

export const TileRenderer: React.FC<TileImageProps> = ({
  tileInfo,
  aspectRatio,
  currentFrame = 0
}) => {
  const [parentEl, setParentEl] = useState<HTMLElement | null>(null)

  const styles = useMemo(() => {
    if (!parentEl) return {}

    const { frameHeight, frameWidth, totalRows } = tileInfo
    const { width: containerWidth, height: containerHeight } = parentEl.getBoundingClientRect()

    const isVertical = frameHeight > frameWidth
    let displayFrameWidth: number
    let displayFrameHeight: number

    if (isVertical) {
      displayFrameHeight = containerHeight
      displayFrameWidth = displayFrameHeight * (frameWidth / frameHeight)
    } else {
      displayFrameWidth = containerWidth
      displayFrameHeight = displayFrameWidth * (frameHeight / frameWidth)
    }

    const naturalWidth = displayFrameWidth * MAX_FRAMES_PER_ROW
    const naturalHeight = displayFrameHeight * totalRows

    const row = Math.floor(currentFrame / MAX_FRAMES_PER_ROW) // 计算当前帧的行号
    const col = currentFrame % MAX_FRAMES_PER_ROW // 计算当前帧的列号

    // 根据行列计算背景偏移量
    const backgroundPositionX = -col * displayFrameWidth
    const backgroundPositionY = - row * displayFrameHeight

    return {
      container: {
        width: displayFrameWidth,
        height: displayFrameHeight,
      },
      image: {
        width: naturalWidth,
        height: naturalHeight,
        backgroundImage: `url(${tileInfo.parsedUrl})`,
        backgroundSize: '100% 100%',
        backgroundPosition: `${backgroundPositionX}px ${backgroundPositionY}px`,
      }
    }
  }, [parentEl, aspectRatio, currentFrame, tileInfo])

  return (
    <div
      data-row={Math.floor(currentFrame / MAX_FRAMES_PER_ROW)}
      data-col={currentFrame % MAX_FRAMES_PER_ROW}
      ref={ref => {
        if (ref?.parentElement) {
          setParentEl(ref.parentElement)
        }
      }}
      className="relative overflow-hidden"
      style={styles.container} // 设置容器的高度
    >
      <div
        className="w-full h-full bg-no-repeat"
        style={styles.image}
      />
    </div>
  )
}

