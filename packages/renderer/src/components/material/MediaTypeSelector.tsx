import React from 'react'
import { MaterialResource } from '@/types/resources'
import { cn } from '@/components/lib/utils'

const tabs = [
  { key: MaterialResource.MediaType.FOLDER, label: '全部' },
  { key: MaterialResource.MediaType.VIDEO, label: '视频' },
  { key: MaterialResource.MediaType.AUDIO, label: '音频' },
  { key: MaterialResource.MediaType.IMAGE, label: '图片' },
]

const editStyles = {
  active: 'text-white',
  inactive: 'text-muted-foreground',
  base: 'rounded text-xs px-2 py-0.5 bg-primary/10',
}

const viewStyles = {
  active: 'bg-primary-highlight1 text-black',
  inactive: 'bg-white/5 text-white',
  base: 'rounded-md px-3 py-2 text-sm border',
}

interface MediaTypeSelectorProps {
  activeTab: 0 | MaterialResource.MediaType
  isEdit?: boolean
  setActiveTab: React.Dispatch<React.SetStateAction<0 | MaterialResource.MediaType>>
  setFilters: React.Dispatch<React.SetStateAction<any>>
  onCreateFolder?: () => void
}

const MediaTypeSelector: React.FC<MediaTypeSelectorProps> = ({
  activeTab,
  isEdit = false,
  setActiveTab,
  setFilters,
  onCreateFolder,
}) => {
  const styles = isEdit ? editStyles : viewStyles
  return (
    <div className="mb-4 flex items-center w-full justify-between">
      <div
        className={cn(
          'flex gap-2 flex-wrap',
        )}
      >
        {tabs.map(tab => (
          <button
            key={tab.key}
            className={cn(activeTab === tab.key ? styles.active : styles.inactive, styles.base)}
            onClick={() => {
              setActiveTab(tab.key) // 更新选中的 Tab
              setFilters(prev => {
                if (tab.key === 0) {
                  const { resType, ...rest } = prev // 移除 resType
                  return rest
                }
                return { ...prev, resType: tab.key }
              })
            }}
          >
            {tab.label}
          </button>
        ))}
      </div>
      {!isEdit && onCreateFolder && (
        <button
          className="ml-4 px-4 py-2 bg-white/5 border text-white text-sm rounded-md hover:bg-primary-highlight1 hover:text-black"
          onClick={onCreateFolder}
        >
          新建文件夹
        </button>
      )}
    </div>
  )
}

export default MediaTypeSelector
