import React, { useCallback, useEffect, useRef } from 'react'
import { ScrollArea } from '@/components/ui/scroll-area'
import { InfiniteData, UseInfiniteQueryResult } from '@tanstack/react-query'
import { Loader2 } from 'lucide-react'
import { PaginatedResult } from '@app/shared/infra/request'

interface HeaderContentItem {
  /**
   * 节点内容
   */
  node: React.ReactNode
  /**
   * 点击事件回调
   */
  onClick?: (event: React.MouseEvent<HTMLDivElement>) => void

  /**
   * 自定义类名
   */
  className?: string
  /**
   * 唯一标识符
   */
  key?: string | number
}

export type HeaderContent = React.ReactNode | HeaderContentItem[]

interface InfiniteResourceListProps<T> {
  /**
   * 无限查询结果
   */
  queryResult: UseInfiniteQueryResult<InfiniteData<PaginatedResult<T>, unknown>, Error>

  /**
   * 渲染单个资源项的函数
   * @param item 资源项数据
   * @param index 资源项索引
   * @returns 渲染的React元素
   */
  renderItem: (item: T, index: number) => React.ReactNode

  /**
   * 空状态时显示的文本
   */
  emptyText?: string

  /**
   * 加载状态时显示的文本
   */
  loadingText?: string

  /**
   * 容器类名
   */
  className?: string

  /**
   * 列表项容器类名
   */
  itemsContainerClassName?: string

  /**
   * 触发加载更多的阈值，默认为200px
   */
  threshold?: number

  /**
   * 头部内容，支持单个节点、节点数组或带事件回调的配置数组
   */
  headerContent?: HeaderContent
}

/**
 * 通用的无限滚动资源列表组件
 * 支持无限滚动加载更多数据
 */
export function InfiniteResourceList<T>({
  queryResult,
  renderItem,
  emptyText = '没有数据',
  loadingText = '加载中...',
  className = '',
  itemsContainerClassName = '',
  threshold = 200,
  headerContent
}: InfiniteResourceListProps<T>) {
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    isError,
    error
  } = queryResult

  const observerTarget = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      entries => {
        if (entries[0].isIntersecting && hasNextPage && !isFetchingNextPage) {
          fetchNextPage()
        }
      },
      {
        rootMargin: `0px 0px ${threshold}px 0px`
      }
    )

    const currentTarget = observerTarget.current
    if (currentTarget) {
      observer.observe(currentTarget)
    }

    return () => {
      if (currentTarget) {
        observer.unobserve(currentTarget)
      }
    }
  }, [fetchNextPage, hasNextPage, isFetchingNextPage, threshold])

  const allItems = data?.pages.flatMap(page => page.list) || []

  const hasHeader = (() => {
    if (Array.isArray(headerContent)) {
      return headerContent.length > 0
    }
    return !!headerContent
  })()
  const isEmpty = !isLoading && allItems.length === 0 && !hasHeader

  const renderItems = useCallback(() => {
    return allItems.map((item, index) => (
      <React.Fragment key={`resource-item-${index}`}>
        {renderItem(item, index)}
      </React.Fragment>
    ))
  }, [allItems, renderItem])

  const renderHeaderContent = useCallback(() => {
    if (!headerContent) return null

    // 检查是否为HeaderContentItem数组
    if (Array.isArray(headerContent) && headerContent.length > 0) {
      const firstItem = headerContent[0]
      const isHeaderContentItemArray = firstItem &&
        typeof firstItem === 'object' &&
        'node' in firstItem

      if (isHeaderContentItemArray) {
        const items = headerContent as HeaderContentItem[]
        return (
          <>
            {items.map((item, index) => (
              <div
                key={item.key ?? `header-item-${index}`}
                className={item.className}
                onClick={item.onClick}

              >
                {item.node}
              </div>
            ))}
          </>
        )
      }
    }

    return headerContent as React.ReactNode
  }, [headerContent])

  return (
    <ScrollArea className={`h-full w-full ${className}`}>
      {isLoading && !data && (
        <div className="flex items-center justify-center p-4 h-full">
          <Loader2 className="w-6 h-6 animate-spin mr-2" />
          <span>{loadingText}</span>
        </div>
      )}

      {isError && (
        <div className="flex items-center justify-center p-4 h-full text-red-500">
          <span>加载失败: {error.message}</span>
        </div>
      )}

      {isEmpty && (
        <div className="flex items-center justify-center p-4 h-full text-gray-500">
          <span>{emptyText}</span>
        </div>
      )}

      {!isEmpty && (
        <div className={itemsContainerClassName}>
          {renderHeaderContent()}
          {renderItems()}

          <div
            ref={observerTarget}
            className="w-full flex justify-center p-2"
          >
            {isFetchingNextPage && (
              <div className="flex items-center">
                <Loader2 className="w-4 h-4 animate-spin mr-2" />
                <span className="text-sm text-gray-500">加载更多...</span>
              </div>
            )}
          </div>
        </div>
      )}
    </ScrollArea>
  )
}

export default InfiniteResourceList
