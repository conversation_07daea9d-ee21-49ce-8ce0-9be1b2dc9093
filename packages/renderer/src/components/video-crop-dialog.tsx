import React, { use<PERSON><PERSON>back, useEffect, useRef, useState } from 'react'
import { <PERSON><PERSON>, Di<PERSON><PERSON>ontent, Di<PERSON>Footer, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { VideoOverlay } from '@app/shared/types/overlay'
import { SUPPORTED_ASPECT_RATIOS } from '@/modules/video-editor/constants'
import ReactCrop, { PercentCrop } from 'react-image-crop'
import 'react-image-crop/dist/ReactCrop.css'
import { useEditorContext } from '@/modules/video-editor/contexts'

interface VideoCropDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  overlay: VideoOverlay
}

export interface CropData {
  x: number
  y: number
  width: number
  height: number
  unit: 'px' | '%'
}

type AspectRatioOption = 'free' | 'original' | typeof SUPPORTED_ASPECT_RATIOS[number]

export const VideoCropDialog: React.FC<VideoCropDialogProps> = ({
  open, onOpenChange, overlay,
}) => {
  const { updateOverlay, getPlayerDimensions } = useEditorContext()

  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [crop, setCrop] = useState<PercentCrop>()
  const [aspectRatioOption, setAspectRatioOption] = useState<AspectRatioOption>('free')
  const [videoLoaded, setVideoLoaded] = useState(false)
  const [videoDimensions, setVideoDimensions] = useState({ width: 0, height: 0 })

  // 计算宽高比数值
  const getAspectRatio = useCallback((option: AspectRatioOption): number | undefined => {
    if (option === 'free') return undefined
    if (option === 'original') {
      return videoDimensions.width / videoDimensions.height
    }

    const [width, height] = option.split(':').map(Number)
    return width / height
  }, [videoDimensions])

  // 重置裁剪区域
  const resetCrop = useCallback(() => {
    if (!videoLoaded) return

    const aspectRatio = getAspectRatio(aspectRatioOption)

    // 设置默认裁剪区域为视频中心的80%
    const defaultCrop: PercentCrop = {
      unit: '%',
      x: 0,
      y: 0,
      width: 100,
      height: 100
    }

    if (aspectRatio) {
      // 根据宽高比调整裁剪区域
      const videoAspectRatio = videoDimensions.width / videoDimensions.height

      if (aspectRatio > videoAspectRatio) {
        // 目标比例更宽，以宽度为准
        defaultCrop.height = (defaultCrop.width / aspectRatio) * videoAspectRatio
        defaultCrop.y = (100 - defaultCrop.height) / 2
      } else {
        // 目标比例更高，以高度为准
        defaultCrop.width = (defaultCrop.height * aspectRatio) / videoAspectRatio
        defaultCrop.x = (100 - defaultCrop.width) / 2
      }
    }

    setCrop(defaultCrop)
  }, [aspectRatioOption, getAspectRatio, videoLoaded, videoDimensions])

  // 处理视频加载完成
  const handleVideoLoad = useCallback(() => {
    const video = videoRef.current
    if (!video) return

    setVideoDimensions({
      width: video.videoWidth,
      height: video.videoHeight
    })
    setVideoLoaded(true)
  }, [])

  // 当宽高比选项改变时重置裁剪区域
  useEffect(() => {
    if (videoLoaded) {
      resetCrop()
    }
  }, [aspectRatioOption, resetCrop, videoLoaded])

  // 初始化裁剪区域
  useEffect(() => {
    if (videoLoaded && !crop) {
      resetCrop()
    }
  }, [videoLoaded, crop, resetCrop])

  // 处理确认裁剪
  const handleConfirm = useCallback(() => {
    if (!crop) return

    const baseWidth = videoDimensions.width * crop.width / 100
    const baseHeight = videoDimensions.height * crop.height / 100
    const { playerWidth, playerHeight } = getPlayerDimensions()
    const targetScale = Math.min(playerWidth / baseWidth, playerHeight / baseHeight)

    const width = baseWidth * targetScale
    const height = baseHeight * targetScale

    updateOverlay(overlay.id, {
      width,
      height,
      left: (playerWidth - width) / 2,
      top: (playerHeight - height) / 2,
      cropData: {
        x: crop.x,
        y: crop.y,
        width: crop.width,
        height: crop.height,
      }
    })
    onOpenChange(false)
  }, [crop, overlay, videoDimensions, onOpenChange, getPlayerDimensions])

  // 处理取消
  const handleCancel = useCallback(() => {
    onOpenChange(false)
  }, [onOpenChange])

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="max-w-4xl max-h-[90vh] overflow-hidden"
        onClick={e => e.stopPropagation()}
        onMouseDown={e => e.stopPropagation()}
        onMouseMove={e => e.stopPropagation()}
      >
        <DialogHeader>
          <DialogTitle>视频画面裁剪</DialogTitle>
        </DialogHeader>

        <div className="flex flex-col space-y-4 overflow-hidden pt-2">
          {/* 裁剪控制选项 */}
          <div className="flex items-center space-x-4">
            <Label htmlFor="aspect-ratio">比例设置:</Label>
            <Select
              value={aspectRatioOption}
              onValueChange={(value: AspectRatioOption) => setAspectRatioOption(value)}
            >
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="free">自由</SelectItem>
                <SelectItem value="original">原始比例</SelectItem>
                {SUPPORTED_ASPECT_RATIOS.map(ratio => (
                  <SelectItem key={ratio} value={ratio}>
                    {ratio}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 裁剪区域 */}
          <div className="flex-1 overflow-hidden flex justify-center">
            <div
              className="relative"
              onClick={e => e.stopPropagation()}
              onMouseDown={e => e.stopPropagation()}
            >
              <ReactCrop
                crop={crop}
                onChange={(_, percentCrop) => setCrop(percentCrop)}
                aspect={getAspectRatio(aspectRatioOption)}
                className="max-w-full"
              >
                <video
                  ref={videoRef}
                  muted
                  src={overlay.localSrc || overlay.src}
                  onLoadedMetadata={handleVideoLoad}
                  className="object-contain"
                  style={{
                    maxHeight: 400
                  }}
                  onClick={e => e.stopPropagation()}
                  onMouseDown={e => e.stopPropagation()}
                />
              </ReactCrop>

              {!videoLoaded && (
                <div className="flex items-center justify-center h-64 bg-gray-100 rounded">
                  <div className="text-gray-500">加载视频中...</div>
                </div>
              )}
            </div>
          </div>

          {/* 视频播放控制 */}
          {/*<div className="border-t pt-4">*/}
          {/*  <VideoCropPlayer*/}
          {/*    src={overlay.src}*/}
          {/*    className="w-full"*/}
          {/*  />*/}
          {/*</div>*/}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={resetCrop} disabled={!videoLoaded}>
            重置
          </Button>
          <Button variant="outline" onClick={handleCancel}>
            取消
          </Button>
          <Button onClick={handleConfirm} disabled={!crop}>
            确认
          </Button>
        </DialogFooter>

        {/* 隐藏的canvas用于处理裁剪数据 */}
        <canvas ref={canvasRef} className="hidden" />
      </DialogContent>
    </Dialog>
  )
}
