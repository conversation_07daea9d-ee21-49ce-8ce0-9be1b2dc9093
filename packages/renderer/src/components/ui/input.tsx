import * as React from 'react'

import { cn } from '@/components/lib/utils'

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  showLength?: boolean
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, showLength, type, ...props }, ref) => {
    return (
      <div className="relative">
        <input
          type={type}
          className={cn(
            'flex h-8 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50',
            className,
          )}
          ref={ref}
          {...props}
        />
        {
          showLength && (
            <div className="absolute bottom-2 right-2 text-xs text-muted-foreground">
              {props.value?.toString()?.length ?? 0}/{props.maxLength}
            </div>
          )
        }
      </div>
    )
  },
)
Input.displayName = 'Input'

export { Input }
