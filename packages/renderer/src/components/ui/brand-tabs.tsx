import { Tabs, Ta<PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs' // 你的 Tabs 组件来源
import React, { ReactNode } from 'react'

interface TabItem {
  value: string;
  label: string;
  content?: ReactNode;
}

interface BrandTabsProps {
  activeTab: string;
  onTabChange: (value: string) => void;
  tabs: TabItem[];
  renderContent?: boolean
}

export function BrandTabs({ activeTab, onTabChange, tabs, renderContent = false }: BrandTabsProps) {
  return (
    <Tabs value={activeTab} onValueChange={onTabChange} className="w-full">
      <TabsList className="h-12 w-full justify-start rounded-none border-0 bg-transparent p-0">
        {tabs.map(tab => (
          <TabsTrigger
            key={tab.value}
            value={tab.value}
            className="h-12 rounded-none border-b-2 border-transparent text-neutral-400
              data-[state=active]:border-primary-highlight 
              data-[state=active]:text-primary-highlight 
              data-[state=active]:bg-transparent 
              data-[state=active]:shadow-none cursor-pointer
            hover:bg-neutral-800/80 hover:text-primary-highlight"

          >
            {tab.label}
          </TabsTrigger>
        ))}
      </TabsList>

      {/* 渲染内容 */}
      {
        renderContent && (
          <div>
            {tabs.map(tab => (
              <div key={tab.value} hidden={activeTab !== tab.value}>
                {tab.content}
              </div>
            ))}
          </div>
        )
      }
     
    </Tabs>
  )
}
