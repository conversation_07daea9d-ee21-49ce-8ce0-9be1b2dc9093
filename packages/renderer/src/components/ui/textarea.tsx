import * as React from 'react'

import { cn } from '@/components/lib/utils'

export interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  showLength?: boolean
}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, showLength, ...props }, ref) => {
    return (
      <div className="relative">
        <textarea
          className={cn(
            'flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50',
            className,
          )}
          ref={ref}
          {...props}
        />
        {
          showLength && (
            <div className="absolute bottom-2 right-2 text-xs text-muted-foreground">
              {props.value?.toString()?.length ?? 0}/{props.maxLength}
            </div>
          )
        }
      </div>
    )
  },
)
Textarea.displayName = 'Textarea'

export { Textarea }
