import React from 'react'
import { useParseUrlFromObjectHref } from '@/hooks/useParseUrlFromObjectHref'
import { useParseObjectIdToUrl } from '@/hooks/useParseObjectIdToUrl'
import { LoadingIndicator } from './LoadingIndicator'

export const AuthedImg: React.FC<React.ImgHTMLAttributes<HTMLImageElement>> = ({ src, ...props }) => {
  const { data: parsedUrl, isFetching } = useParseUrlFromObjectHref(src)

  if (isFetching) {
    return <LoadingIndicator />
  }

  return (
    <img
      src={parsedUrl || ''}
      {...props}
    />
  )
}

export const AuthedImgByObjectId: React.FC<React.ImgHTMLAttributes<HTMLImageElement>> = ({ src, ...props }) => {
  const { data: parsedUrl, isFetching } = useParseObjectIdToUrl(src)

  if (isFetching) {
    return <LoadingIndicator />
  }

  return (
    <img
      src={parsedUrl || ''}
      {...props}
    />
  )
}

