import { SidebarProvider } from '@/components/ui/sidebar'
import React from 'react'
import { useVirtualTab } from '@/contexts'
import VideoEditorPage from '@/modules/video-editor/video-editor.page'

const VideoEditor: React.FC = () => {
  const { params: { id, projectId, name } } = useVirtualTab('Editor')

  if (!id || !projectId) return null

  return (
    <SidebarProvider id="SidebarProvider">
      <VideoEditorPage scriptId={id} projectId={projectId} scriptName={name} />
    </SidebarProvider>
  )
}

export default VideoEditor
