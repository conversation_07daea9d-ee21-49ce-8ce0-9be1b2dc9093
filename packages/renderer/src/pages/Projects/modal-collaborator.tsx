import { ModalContent } from '@/components/modal'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Separator } from '@/components/ui/separator'
import { useQueryCollaborators } from '@/hooks/queries/useQueryCollaborator'
import { useQueryTeamMemberByID, useQueryTeamMemberList } from '@/hooks/queries/useQueryTeam'
import { Collaborator } from '@/libs/request/api/collaborator'
import { useModal } from '@/libs/tools/modal'
import { Project } from '@/types/project'
import { BrushCleaning, Search } from 'lucide-react'
import React, { useMemo } from 'react'
import { TokenManager } from '@/libs/storage'
import { useModalInviteMember } from '@/pages/Team/modal-invite-member'

function ModalCollaborator({ project }: { project: Project }) {
  const modalInviteMember = useModalInviteMember()
  const [keyword, setKeyword] = React.useState('')
  const { data: memberList, isLoading: loadingMembers } = useQueryTeamMemberList()
  const { data: collaborators, isLoading: loadingCollaborators } = useQueryCollaborators(project.id)
  const { data: currentMember } = useQueryTeamMemberByID(TokenManager.getUserId())

  const aggregatedCollaborators = useMemo(() => {
    const defaultCollaborators: Collaborator[] =
      memberList
        ?.filter(member => member.roles?.some(role => ['owner', 'admin'].includes(role.code)))
        .map(member => ({ ...member, memberId: member.id, projectId: project.id, source: 'manual' })) || []
    return [...defaultCollaborators, ...(collaborators || [])].filter(c => c.nickname.includes(keyword))
  }, [memberList, collaborators, keyword, project.id])
  const filteredMemberList = useMemo(() => {
    if (!memberList) return []
    return memberList
      .filter(member => member.nickname.includes(keyword))
      .filter(member => !aggregatedCollaborators.some(c => c.memberId === member.id))
  }, [memberList, aggregatedCollaborators, keyword])

  return (
    <ModalContent
      title={
        <div className="flex items-center justify-between pr-2">
          <div className="flex items-center gap-2">
            <span>添加协作者</span>
            <Badge variant="outline">
              <span className="font-normal">当前项目：</span>
              {project.projectName}
            </Badge>
          </div>
          <div className="relative font-normal">
            <Input
              placeholder="请输入关键词搜索"
              value={keyword}
              onChange={e => setKeyword(e.target.value)}
              className="pr-10"
            />
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 size-4 text-muted-foreground" />
          </div>
        </div>
      }
    >
      <div className="flex flex-1 min-h-0">
        <div className="flex-1 min-h-0">
          <div className="h-6 flex items-center">
            <span>团队成员列表</span>
          </div>
          <Separator className="my-1" />
          {loadingMembers ? (
            <div className="flex items-center justify-center h-20">
              <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-blue-500" />
            </div>
          ) : (
            <div className="flex flex-col gap-2 overflow-auto h-full">
              {filteredMemberList.map(member => (
                <div
                  key={member.id}
                  className="flex items-center justify-start p-2 hover:bg-muted-foreground/5 rounded"
                >
                  <Avatar>
                    <AvatarFallback style={{ backgroundImage: `linear-gradient(${member.id})` }}>
                      {member.nickname.slice(0, 1).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex flex-col ml-2 gap-0.5">
                    <span>{member.nickname}</span>
                    <div className="flex items-center gap-1 text-xs text-muted-foreground h-4.5">
                      {member.roles?.map(role => (
                        <Badge key={role.id} className="px-1 rounded font-normal leading-none" variant="outline">
                          {role.name}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  {member.roles?.some(role => !['owner', 'admin'].includes(role.code)) && (
                    <Button variant="link" size="sm" className="ml-auto">
                      添加
                    </Button>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
        <Separator orientation="vertical" className="mx-4" />
        <div className="flex-1">
          <div className="flex justify-between items-center h-6">
            <span>协作者列表</span>
            <Button variant="ghost" size="icon" className="size-6 rounded" title="清空协作者列表">
              <BrushCleaning className="size-4" />
            </Button>
          </div>
          <Separator className="my-1" />
          <div className="flex flex-col gap-2 overflow-auto h-full">
            {loadingCollaborators ? (
              <div className="flex items-center justify-center h-20">
                <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-blue-500" />
              </div>
            ) : (
              aggregatedCollaborators?.map(collaborator => (
                <div className="flex items-center justify-start p-2 hover:bg-muted-foreground/5 rounded">
                  <Avatar>
                    <AvatarFallback style={{ backgroundImage: `linear-gradient(${collaborator.id})` }}>
                      {collaborator.nickname.slice(0, 1).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <span className="ml-2">{collaborator.nickname}</span>
                  {collaborator.roles?.some(role => !['owner', 'admin'].includes(role.code)) && (
                    <Button variant="link" size="sm" className="ml-auto">
                      移除
                    </Button>
                  )}
                </div>
              ))
            )}
          </div>
        </div>
      </div>
      {currentMember?.roles?.some(role => ['owner', 'admin'].includes(role.code)) && (
        <Button variant="default" className="absolute left-6 bottom-6" onClick={() => modalInviteMember()}>
          邀请成员
        </Button>
      )}
    </ModalContent>
  )
}

export function useProjectCollaboratorModal() {
  const modal = useModal()

  return (project: Project) => {
    modal({
      className: 'w-2xl h-120 max-w-full!',
      content: <ModalCollaborator project={project} />,
    })
  }
}
