import React, { useState } from 'react'
import './login.css'
import { Lock, Smartphone } from 'lucide-react'
import { AuthModule } from '@/libs/request/api/auth'
import { TokenManager } from '@/libs/storage'
import { useNavigate } from 'react-router'
import { z } from 'zod'
import { useQueryClient } from '@tanstack/react-query'

const mobileSchema = z
  .string()
  .min(11, '手机号太短')
  .max(11, '手机号太长')
  .regex(/^1[3-9]\d{9}$/, '请输入正确的手机号格式')
const codeSchema = z.string().min(4, '请输入密码').max(6, '密码长度不正确').regex(/^\d+$/, '密码只能包含数字')

const safe = <Fn extends (...args: any[]) => any>(fn: Fn) => {
  return (...args: Parameters<Fn>): ReturnType<Fn> | void => {
    try {
      return fn(...args)
    } catch (error) {
      console.error(error)
    }
  }
}

export default function Login() {
  const queryClient = useQueryClient()
  const navigate = useNavigate()
  const [code, setCode] = useState('')
  const [codeInit, setCodeInit] = useState(false)
  const [codeError, setCodeError] = useState('')
  const [mobile, setMobile] = useState('')
  const [mobileInit, setMobileInit] = useState(false)
  const [mobileError, setMobileError] = useState('')
  const [error, setError] = useState('')

  const revalidMobile = (value?: string) => {
    setMobileInit(true)
    const result = mobileSchema.safeParse(value ?? mobile)
    if (result.success) {
      setMobileError('')
      return true
    }
    setMobileError(result.error.errors[0].message)
  }

  const revalidCode = (value?: string) => {
    setCodeInit(true)
    const result = codeSchema.safeParse(value ?? code)
    if (result.success) {
      setCodeError('')
      return true
    }
    setCodeError(result.error.errors[0].message)
  }

  const login = safe(async () => {
    if (!revalidCode() || !revalidMobile()) return
    try {
      // 按验证码流程：先请求验证码，再使用输入的“密码”作为验证码登录
      await AuthModule.sendSMSCode({ mobile, scene: 1 })
      const loginData = await AuthModule.codeLogin({ mobile, code: Number(code) })
      TokenManager.saveLoginData(loginData)
      queryClient.clear()
      navigate('team', { replace: true })
    } catch (error: any) {
      setError(error?.message || '登录失败')
    }
  })

  return (
    <div className="absolute inset-0 flex flex-col items-stretch justify-start z-10 px-30 py-37">
      <div className="text-4xl">欢迎登录</div>
      <div className="flex flex-col mt-15 gap-6">
        <div className="flex-1 flex relative">
          <input
            className="bg-[hsla(0,0%,96%,0.05)] rounded-full flex-1 pl-13 h-14"
            placeholder="请输入手机号"
            value={mobile}
            onChange={e => {
              setMobile(e.currentTarget.value)
              if (mobileInit) revalidMobile(e.currentTarget.value)
              else setMobileError('')
            }}
            onBlur={() => revalidMobile()}
          />
          <Smartphone className="absolute left-4 top-1/2 -translate-y-1/2 size-6" />
          {mobileError && (
            <p className="absolute left-5 bottom-0 translate-y-1/1 text-destructive text-sm">{mobileError}</p>
          )}
        </div>
        <div className="flex-1 flex relative">
          <input
            className="bg-[hsla(0,0%,96%,0.05)] rounded-full flex-1 pl-13 h-14"
            placeholder="请输入密码"
            value={code}
            type="password"
            onChange={e => {
              setCodeError('')
              if (!/\d*/.test(e.currentTarget.value)) return
              setCode(e.currentTarget.value)
              if (codeInit) revalidCode(e.currentTarget.value)
              else setCodeError('')
            }}
            onBlur={() => revalidCode()}
          />
          <Lock className="absolute left-4 top-1/2 -translate-y-1/2 size-6" />
          {codeError && (
            <p className="absolute left-5 bottom-0 translate-y-1/1 text-sm text-destructive">{codeError}</p>
          )}
          {/* 移除原“获取验证码”按钮，登录时自动请求验证码 */}
        </div>
      </div>
      <div className="mt-auto flex relative">
        <div
          className="[background:linear-gradient(91.55deg,rgba(0,246,254,0.6)1.6%,rgba(255,106,0,0.6)99.15%)]
                absolute -inset-3 rounded-full blur-xs"
        />
        <button className="flex-1 h-16 rounded-full bg-black z-10 cursor-pointer" onClick={login}>
          登录
        </button>
        {error && <p className="absolute left-5 bottom-0 translate-y-2/1 text-sm text-destructive">{error}</p>}
      </div>
    </div>
  )
}
