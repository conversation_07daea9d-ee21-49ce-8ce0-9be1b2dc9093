import React, { useEffect } from 'react'
import { Outlet } from 'react-router'
import './login.css'

export default function Login() {
  useEffect(() => {
    const isDark = document.documentElement.classList.contains('dark')
    if (isDark) return
    document.documentElement.classList.add('dark')
    return () => {
      document.documentElement.classList.remove('dark')
    }
  })

  useEffect(() => {
    const root = document.documentElement
    const originalFontSize = root.style.fontSize

    const setFontSize = () => {
      let size = (window.innerWidth / 1920) * 16
      if (window.innerWidth < 640) size *= 2
      root.style.fontSize = `${size}px`
    }

    setFontSize()
    window.addEventListener('resize', setFontSize)

    return () => {
      window.removeEventListener('resize', setFontSize)
      root.style.fontSize = originalFontSize // ✅ 恢复初始值
    }
  }, [])

  return (
    <div
      className="h-full bg-[url(/images/bg-login.jpg)]
        bg-cover bg-center bg-no-repeat relative
        before:content-[''] before:h-full before:inset-0 before:absolute before:bg-black/50
        *:relative *:z-10 flex items-center"
    >
      <div className="ml-40 text-7xl leading-25 select-none font-[ShuHeiti] font-semibold whitespace-nowrap hidden sm:block">
        <span className="text-gradient-brand">AI视频</span>智造新次元
        <br />从<span className="text-gradient-brand">创作到分发</span>，<br />
        十倍效能跃迁
      </div>
      <div className="absolute! -translate-x-1/2 sm:translate-x-0 left-1/2 login-box w-170 h-180 rounded-[20px] border-2 *:blur-sm *:last:blur-none">
        <div className="size-5/6 absolute -top-[3px] -left-[3px] middle-1" />
        <div className="size-5/6 absolute -top-[4px] -left-[4px] outer-1" />
        <div className="size-5/6 absolute top-0 left-0 inner-1-1" />
        <div className="size-5/6 absolute top-0 left-0 inner-1-2" />
        <div className="size-5/6 rotate-180 absolute -bottom-[3px] -right-[3px] middle-2" />
        <div className="size-5/6 rotate-180 absolute -bottom-[4px] -right-[4px] outer-2" />
        <div className="size-5/6 rotate-180 absolute bottom-0 right-0 inner-2-1" />
        <div className="size-5/6 rotate-180 absolute bottom-0 right-0 inner-2-2" />
        <div className="login-content size-full relative rounded-[20px]">
          <Outlet />
        </div>
      </div>
    </div>
  )
}
