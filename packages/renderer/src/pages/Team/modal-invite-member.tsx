import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>box<PERSON><PERSON>, CheckboxProvider, useCheckboxContext } from '@/components/checkbox'
import { ModalContent } from '@/components/modal'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { useQueryProjectList } from '@/hooks/queries/useQueryProject'
import { useQueryTeamRoles } from '@/hooks/queries/useQueryTeam'
import { usePending } from '@/hooks/usePending'
import { TeamAPI } from '@/libs/request/api/team'
import { useModal } from '@/libs/tools/modal'
import { Loader2, Search } from 'lucide-react'
import React from 'react'
import { toast } from 'react-toastify'

function InviteMemberModal() {
  const { data } = useQueryTeamRoles()
  const roles = React.useMemo(() => data?.filter(role => role.code !== 'owner') || [], [data])
  const [selectedRole, setSelectedRole] = React.useState<string>('')
  const { selected, all, setSelected } = useCheckboxContext<number>()
  const { data: projectList } = useQueryProjectList()
  const [keyword, setKeyword] = React.useState('')

  return (
    <ModalContent
      title="邀请成员"
      description="请选择新成员的角色与项目权限"
      buttons={() => {
        const { pending, withPending } = usePending()

        return (
          <Button
            className="w-full"
            disabled={!selectedRole || pending}
            onClick={withPending(async () => {
              const roleId = roles.find(role => role.code === selectedRole)?.id
              if (!roleId) return
              const code = await TeamAPI.invite({
                projectIds: Object.values(selected),
                roleIds: [roleId],
              })
              navigator.clipboard.writeText(code)
              toast.success('邀请链接已复制到剪贴板')
            })}
          >
            {pending ? <Loader2 className="animate-spin size-4" /> : '复制邀请码'}
          </Button>
        )
      }}
    >
      <div className="flex items-center justify-between">
        <span>权限角色</span>
        <Select
          value={selectedRole}
          onValueChange={code => {
            setSelectedRole(code)
            if (code === 'admin') Object.keys(all).forEach(id => setSelected(id, true))
          }}
        >
          <SelectTrigger className="w-48">
            <SelectValue placeholder="选择角色" />
          </SelectTrigger>
          <SelectContent>
            {roles.map(role => (
              <SelectItem key={role.id} value={role.code}>
                {role.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <Separator />
      <div className="flex flex-col gap-2">
        <div className="flex items-center">
          <div className="relative text-sm">
            <Input
              placeholder="搜索项目"
              value={keyword}
              onChange={e => setKeyword(e.target.value)}
              className="pr-10 h-8"
            />
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 size-4 text-muted-foreground" />
          </div>
          <span className="ml-auto text-xs text-muted-foreground">
            已选择：{Object.values(selected).length}/{Object.values(all).length}
          </span>
          <CheckboxHeader className="ml-2" disabled={selectedRole === 'admin'} />
        </div>
        <div className="max-h-80 overflow-y-scroll -mr-4.5 pr-3">
          {projectList?.map(project => (
            <div
              className="flex items-center aria-hidden:hidden"
              aria-hidden={!project.projectName.includes(keyword)}
              key={project.id}
            >
              <Badge variant="default" className="px-1 rounded leading-none">
                项目
              </Badge>
              <span className="ml-2">{project.projectName}</span>
              <CheckboxItem className="ml-auto" value={project.id} disabled={selectedRole === 'admin'} />
            </div>
          ))}
        </div>
      </div>
    </ModalContent>
  )
}

export function useModalInviteMember() {
  const modal = useModal()

  return () =>
    modal({
      content: (
        <CheckboxProvider>
          <InviteMemberModal />
        </CheckboxProvider>
      ),
    })
}
