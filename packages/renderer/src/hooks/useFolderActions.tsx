import React, { useMemo } from 'react'
import { Edit, FolderInput, FolderPlus, Trash, SquareMenu } from 'lucide-react'
import { FolderActionKeys, actionLabels, NormalModeParams, RecycleModeParams } from '@/types/resources'
import { useItemActions } from '@/hooks/useItemActions'
import { TreeNode } from '@/components/TreeList'
import { FolderAction } from '@/components/material/MediaItem'

type FolderNormalModeParams = NormalModeParams & {
  childFolders: TreeNode[]
}

export type UseFolderActionsParams = FolderNormalModeParams | RecycleModeParams

// 判断当前是否为回收站模式
function isRecycleMode(
  params: UseFolderActionsParams
): params is RecycleModeParams {
  return 'isRecycle' in params && params.isRecycle === true
}

export const useFolderActions = (params: UseFolderActionsParams) => {
  const { createItem, renameItem, deleteItem, completeDeleteItem, backItem } = useItemActions()

  return useMemo<FolderAction[]>(() => {
    if (isRecycleMode(params)) {
      return [
        {
          icon: <FolderInput className="w-4 h-4" />,
          label: actionLabels[FolderActionKeys.BACK],
          value: FolderActionKeys.BACK,
          onClick: (nodeId: string, _parentId: string, label: string) => {
            backItem(params.type, nodeId, label)
          },
        },
        {
          icon: <Trash className="w-4 h-4" />,
          label: actionLabels[FolderActionKeys.COMPLETELY_DELETE],
          value: FolderActionKeys.COMPLETELY_DELETE,
          onClick: (nodeId: string, _parentId: string, label: string) => {
            completeDeleteItem(params.type, nodeId, label, true)
          },
        },
      ]
    }

    const actions: FolderAction[] = [
      {
        icon: <FolderInput className="w-4 h-4" />,
        label: actionLabels[FolderActionKeys.MOVE],
        value: FolderActionKeys.MOVE,
        onClick: nodeId => {
          params.setMoveType(params.type)
          params.setMoveId(nodeId)
          params.setMoveDialogOpen(true)
        },
      },
      {
        icon: <Edit className="w-4 h-4" />,
        label: actionLabels[FolderActionKeys.RENAME],
        value: FolderActionKeys.RENAME,
        onClick: (nodeId, _parentId, label) =>
          renameItem(params.type, nodeId, label!, {
            label: '文件夹名称',
            headerTitle: '文件夹',
          }, true, params.childFolders),
      },
      {
        icon: <Trash className="w-4 h-4" />,
        label: actionLabels[FolderActionKeys.DELETE],
        value: FolderActionKeys.DELETE,
        onClick: (nodeId, _parentId, label) => {
          if (params.isLocal) {
            return completeDeleteItem(params.type, nodeId, label!)
          }
          return deleteItem(params.type, nodeId, label!)
        },
      },
    ]
    if (!params.isLocal) {
      actions.unshift({
        icon: <FolderPlus className="w-4 h-4" />,
        label: actionLabels[FolderActionKeys.CREATE],
        value: FolderActionKeys.CREATE,
        onClick: nodeId =>
          createItem(params.type, nodeId, {
            label: '文件夹名称',
            headerTitle: '文件夹',
          }, params.childFolders),
      })
      actions.splice(actions.length - 1, 0, {
        icon: <SquareMenu className="w-4 h-4" />,
        label: actionLabels[FolderActionKeys.DETAILS],
        value: FolderActionKeys.DETAILS,
        onClick: nodeId => console.log('查看文件夹详细信息', nodeId),
      })
    }

    return actions
  }, [params, createItem, renameItem, deleteItem, completeDeleteItem, backItem])
}
