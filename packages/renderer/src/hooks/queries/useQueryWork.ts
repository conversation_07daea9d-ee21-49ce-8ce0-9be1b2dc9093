import { QUERY_KEYS } from '../../constants/queryKeys'
import { ResourceModule } from '../../libs/request/api/resource'
import { useInfiniteQuery } from '../useInfiniteQuery'
import { PaginationParams } from '@app/shared/infra/request'

type WorkQueryParams = PaginationParams & {
  associated?: string
  auditStatus?: string
  composeStatus?: string
  createAt?: string
  distStatus?: string
  hadDownload?: string
  keyword?: string
  product?: string
  projectId?: string
  repetitionRateRange?: string
  scriptId?: string
  sortField?: string
  sortOrder?: string
}

/**
 * 使用无限查询获取作品列表，支持无限滚动加载
 * @param params 查询参数，包括分类ID和每页大小
 * @returns 无限查询结果
 */
export const useInfiniteQueryWorkList = (params: WorkQueryParams = {}) => {
  return useInfiniteQuery([QUERY_KEYS.WORK_LIST], ResourceModule.work.list, params, {
    pageSize: params.pageSize || 12,
  })
}
