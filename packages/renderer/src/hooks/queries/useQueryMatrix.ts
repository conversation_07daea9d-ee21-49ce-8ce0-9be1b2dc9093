import { useQuery } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { MatrixModule } from '@/libs/request/api/matrix'
import { usePagination } from '../usePagination'
import { AccountPushDetail, AccountPushDetailRequestParams, TimeRangeParams } from '@/types/matrix/douyin'

/**
 * 获取抖音账号概览数据
 * @param params 时间范围参数
 * @param enabled 是否启用查询
 */
export const useQueryDyAccountOverview = (
  params?: TimeRangeParams,
) => {
  return useQuery({
    queryKey: [QUERY_KEYS.DY_ACCOUNT_OVERVIEW, params],
    queryFn: () => MatrixModule.dyAccount.overview(params),
  })
}

export const usePaginationPlanDetailList = (
  searchParams: AccountPushDetailRequestParams = {},
  initialPageSize: number = 20,
  enabled?: boolean,
  refetchInterval?: number
) => {
  return usePagination<AccountPushDetail, AccountPushDetailRequestParams>({
    queryKey: [QUERY_KEYS.ACCOUNT_PUSH_DETAIL],
    queryFn: MatrixModule.dyAccount.accountPushDetail,
    searchParams: searchParams,
    initialPageSize,
    enabled: enabled ? enabled : true,
    staleTime: 0,
    gcTime: 0,
    refetchInterval: refetchInterval || undefined
  })
}

