import { QUERY_KEYS } from '@/constants/queryKeys'
import { OssModule } from '@/libs/request/api/oss'
import { parseUrlFromATag } from '@/libs/tools/resource'
import { useQuery } from '@tanstack/react-query'

export const useParseObjectIdToUrl = (objectId?: string) => {
  return useQuery({
    queryKey: [QUERY_KEYS.URL_FROM_OBJECT_ID, objectId],
    queryFn: async () => {
      if (!objectId) return null
      return OssModule.getObjectHref(objectId).then(parseUrlFromATag)
    },
    enabled: !!objectId
  })
}