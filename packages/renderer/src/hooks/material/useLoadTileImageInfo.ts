import { useQuery } from '@tanstack/react-query'
import { parseUrlFromObjectHref } from '@/libs/tools/resource'

/**
 * 瓦片图中每一行最多展示 10 个关键帧
 */
const MAX_FRAMES_PER_ROW = 10

export type TileImageInfo = {
  /**
   * 解析后的瓦片图 URL
   */
  parsedUrl: string

  /**
   * 瓦片图中, 每一帧图片的宽度
   */
  frameWidth: number

  /**
   * 瓦片图中, 每一帧图片的高度
   */
  frameHeight: number

  /**
   * 瓦片图中, 总共包含的帧数
   */
  totalFrames: number

  /**
   * 瓦片图中, 总共包含的行数. (一行最多容纳 {MAX_FRAMES_PER_ROW} 帧)
   * @see {MAX_FRAMES_PER_ROW}
   */
  totalRows: number
}

export const useLoadTileImageInfo = (url: string) => {
  const { data: tileInfo } = useQuery({
    queryKey: ['TILE_IMAGE_INFO', url],
    queryFn: async () => {
      if (!url) return null

      const href = await parseUrlFromObjectHref(url)
      if (!href) return

      // 文件名示例：d310401c-84ad-4fb3-a3ce-e14da89a090f_216x384_1_tile_00001.jpg
      const match = href.match(/_(\d+)x(\d+)_(\d+)_/)
      if (!match || match.length < 4) return

      const frameWidth = Number(match[1])
      const frameHeight = Number(match[2])
      const totalFrames = Number(match[3])
      const totalRows = Math.ceil(totalFrames / MAX_FRAMES_PER_ROW)

      return {
        parsedUrl: href,
        frameWidth,
        frameHeight,
        totalFrames,
        totalRows
      }
    }
  })

  return tileInfo
}
