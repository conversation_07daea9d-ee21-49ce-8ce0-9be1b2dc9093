import { InfiniteData, useInfiniteQuery as useReactInfiniteQuery, UseInfiniteQueryResult } from '@tanstack/react-query'

import { PaginatedResult } from '@app/shared/infra/request'

/**
 * 分页查询参数接口
 */
export interface PaginationQueryParams {
  pageNo?: number
  pageSize?: number
  [key: string]: any
}

/**
 * 分页查询函数类型
 * @param params 查询参数
 * @returns 分页结果
 */
export type PaginationQueryFn<T> = (params: PaginationQueryParams) => Promise<PaginatedResult<T>>

/**
 * 通用的无限查询钩子
 * 适配所有分页接口，减少重复代码
 *
 * @param queryKey 查询键
 * @param queryFn 查询函数
 * @param params 查询参数
 * @param options 额外配置选项
 * @returns 无限查询结果
 */
export function useInfiniteQuery<T>(
  queryKey: unknown[],
  queryFn: PaginationQueryFn<T>,
  params: Omit<PaginationQueryParams, 'pageNo'> = {},
  options: {
    enabled?: boolean
    pageSize?: number
    initialPageParam?: number
    getNextPageParam?: (lastPage: PaginatedResult<T>, allPages: PaginatedResult<T>[]) => number | undefined
  } = {}
): UseInfiniteQueryResult<InfiniteData<PaginatedResult<T>, unknown>, Error> {
  // 默认参数
  const pageSize = options.pageSize || 20
  const initialPageParam = options.initialPageParam || 1

  // 默认的获取下一页函数
  const defaultGetNextPageParam = (lastPage: PaginatedResult<T>, allPages: PaginatedResult<T>[]) => {
    // 如果已加载的数据小于总数，则返回下一页的页码
    const loadedItemsCount = allPages.reduce((acc, page) => acc + page.list.length, 0)
    if (loadedItemsCount < lastPage.total) {
      return allPages.length + 1
    }
    // 返回undefined表示没有更多数据了
    return undefined
  }

  return useReactInfiniteQuery({
    // eslint-disable-next-line @tanstack/query/exhaustive-deps
    queryKey: [...queryKey, 'infinite', { ...params, pageSize }],
    queryFn: ({ pageParam = initialPageParam }) => {
      return queryFn({
        ...params,
        pageNo: pageParam,
        pageSize
      })
    },
    initialPageParam,
    getNextPageParam: options.getNextPageParam || defaultGetNextPageParam,
    enabled: options.enabled !== undefined ? options.enabled : true
  })
}

export default useInfiniteQuery
