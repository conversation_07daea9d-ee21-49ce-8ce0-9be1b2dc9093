import type { AppModule } from '../infra/types/app-module/AppModule.js'
import { ModuleContext } from '../infra/types/app-module/ModuleContext.js'
import { BrowserWindow, screen } from 'electron'
import type { AppInitConfig } from '../AppInitConfig.js'

class WindowManager implements AppModule {

  readonly #preload: { path: string }
  readonly #renderer: { path: string } | URL
  readonly #openDevTools

  constructor({ initConfig, openDevTools = false }: { initConfig: AppInitConfig, openDevTools?: boolean }) {
    this.#preload = initConfig.preload
    this.#renderer = initConfig.renderer
    this.#openDevTools = openDevTools
  }

  async enable({ app }: ModuleContext): Promise<void> {
    await app.whenReady()
    await this.restoreOrCreateWindow(true)
    app.on('second-instance', () => this.restoreOrCreateWindow(true))
    app.on('activate', () => this.restoreOrCreateWindow(true))
  }

  async createWindow(): Promise<BrowserWindow> {
    const displays = screen.getAllDisplays()
    const targetDisplay = displays[1] || displays[0]
    const { x, y, width, height } = targetDisplay.workArea

    const browserWindow = new BrowserWindow({
      x: x,
      y: y,
      width,
      height,
      show: false, // Use the 'ready-to-show' event to show the instantiated BrowserWindow.
      frame: false,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        sandbox: false, // Sandbox disabled because the demo of preload script depend on the Node.js api
        webviewTag: false, // The webview tag is not recommended. Consider alternatives like an iframe or Electron's BrowserView. @see https://www.electronjs.org/docs/latest/api/webview-tag#warning
        preload: this.#preload.path,
        webSecurity: false, // 允许加载本地资源
      },
    })

    if (this.#renderer instanceof URL) {
      await browserWindow.loadURL(this.#renderer.href)
    }
    else {
      await browserWindow.loadFile(this.#renderer.path)
    }

    return browserWindow
  }

  async restoreOrCreateWindow(show = false) {
    let window = BrowserWindow.getAllWindows().find(w => !w.isDestroyed())

    if (window === undefined) {
      window = await this.createWindow()
    }

    if (!show) {
      return window
    }

    if (window.isMinimized()) {
      window.restore()
    }

    window?.show()

    if (this.#openDevTools) {
      window?.webContents.openDevTools()
    }

    window.focus()

    return window
  }
}

export function createWindowManagerModule(...args: ConstructorParameters<typeof WindowManager>) {
  return new WindowManager(...args)
}
