import { RenderTask } from '@app/shared/types/local-db/entities/render-task.entity.js'

/**
 * 渲染任务领域模型类
 * 专门用于管理视频渲染任务
 */
export class RenderTaskModel {

  /**
   * 任务ID
   */
  id: number

  /**
   * 团队ID
   */
  team_id: number

  /**
   * 用户ID
   */
  uid: string

  /**
   * 云端任务编号
   */
  task_no: string

  /**
   * 视频名称
   */
  name: string

  /**
   * 封面图URL
   */
  cover_url: string

  /**
   * 封面图对象ID
   */
  cover_object_id: string

  /**
   * 脚本ID
   */
  script_id: number

  /**
   * 渲染数据对象ID
   */
  object_id: string

  /**
   * 分辨率
   */
  resolution: string

  /**
   * 帧率
   */
  fps: number

  /**
   * 时长(秒)
   */
  duration: number

  /**
   * 渲染进度 (0-100)
   */
  progress: number

  /**
   * 任务状态
   */
  status: RenderTask.RenderTaskStatus

  /**
   * 失败原因
   */
  reason: string

  /**
   * 文件大小(字节)
   */
  file_size: number

  /**
   * 下载链接
   */
  download_url: string

  /**
   * 渲染开始时间戳
   */
  render_start_time: number

  /**
   * 渲染结束时间戳
   */
  render_end_time: number

  /**
   * 删除时间戳
   */
  deleted_at: number

  /**
   * 更新时间戳
   */
  updated_at: number

  /**
   * 创建时间戳
   */
  created_at: number

  constructor(data: Partial<RenderTask.IRenderTask> = {}) {
    this.id = data.id ?? 0
    this.team_id = data.team_id ?? 0
    this.uid = data.uid ?? ''
    this.task_no = data.task_no ?? ''
    this.name = data.name ?? ''
    this.cover_url = data.cover_url ?? ''
    this.cover_object_id = data.cover_object_id ?? ''
    this.script_id = data.script_id ?? 0
    this.object_id = data.object_id ?? ''
    this.resolution = data.resolution ?? ''
    this.fps = data.fps ?? 30
    this.duration = data.duration ?? 0
    this.progress = data.progress ?? 0
    this.status = data.status ?? RenderTask.RenderTaskStatus.WAITING
    this.reason = data.reason ?? ''
    this.file_size = data.file_size ?? 0
    this.download_url = data.download_url ?? ''
    this.render_start_time = data.render_start_time ?? 0
    this.render_end_time = data.render_end_time ?? 0
    this.deleted_at = data.deleted_at ?? 0
    this.updated_at = data.updated_at ?? Date.now()
    this.created_at = data.created_at ?? Date.now()
  }

  /**
   * 判断是否已删除
   */
  isDeleted(): boolean {
    return this.deleted_at > 0
  }

  /**
   * 判断是否正在渲染
   */
  isRendering(): boolean {
    return this.status === RenderTask.RenderTaskStatus.RENDERING
  }

  /**
   * 判断是否已完成
   */
  isCompleted(): boolean {
    return this.status === RenderTask.RenderTaskStatus.COMPLETED
  }

  /**
   * 判断是否失败
   */
  isFailed(): boolean {
    return this.status === RenderTask.RenderTaskStatus.FAILED
  }

  /**
   * 判断是否可以下载
   */
  canDownload(): boolean {
    return this.isCompleted() && !!this.download_url
  }

  /**
   * 标记为已删除
   */
  markAsDeleted(): void {
    this.deleted_at = Date.now()
    this.updated_at = Date.now()
  }

  /**
   * 更新进度
   */
  updateProgress(progress: number): void {
    this.progress = Math.max(0, Math.min(100, progress))
    this.updated_at = Date.now()
  }

  /**
   * 更新状态
   */
  updateStatus(status: RenderTask.RenderTaskStatus, reason?: string): void {
    this.status = status
    if (reason !== undefined) {
      this.reason = reason
    }
    this.updated_at = Date.now()

    // 记录渲染时间
    if (status === RenderTask.RenderTaskStatus.RENDERING && this.render_start_time === 0) {
      this.render_start_time = Date.now()
    } else if (
      (status === RenderTask.RenderTaskStatus.COMPLETED || status === RenderTask.RenderTaskStatus.FAILED) &&
      this.render_end_time === 0
    ) {
      this.render_end_time = Date.now()
    }
  }

  /**
   * 更新文件信息
   */
  updateFileInfo(fileSize: number, downloadUrl: string): void {
    this.file_size = fileSize
    this.download_url = downloadUrl
    this.updated_at = Date.now()
  }

  /**
   * 获取渲染耗时(毫秒)
   */
  getRenderDuration(): number {
    if (this.render_start_time === 0) return 0
    const endTime = this.render_end_time || Date.now()
    return endTime - this.render_start_time
  }

  /**
   * 将渲染任务转换为JSON对象
   */
  toJSON(): RenderTask.IRenderTask {
    return {
      id: this.id,
      team_id: this.team_id,
      uid: this.uid,
      task_no: this.task_no,
      name: this.name,
      cover_url: this.cover_url,
      cover_object_id: this.cover_object_id,
      script_id: this.script_id,
      object_id: this.object_id,
      resolution: this.resolution,
      fps: this.fps,
      duration: this.duration,
      progress: this.progress,
      status: this.status,
      reason: this.reason,
      file_size: this.file_size,
      download_url: this.download_url,
      render_start_time: this.render_start_time,
      render_end_time: this.render_end_time,
      deleted_at: this.deleted_at,
      updated_at: this.updated_at,
      created_at: this.created_at
    }
  }
}
