import path from 'path'
import { promises as fsPromises } from 'fs'
import { ResourceCacheType } from '@app/shared/types/resource-cache.types.js'
import { app } from 'electron'

const CACHE_BASE_DIR = path.join(app.getPath('userData'), 'cache', 'resources')

/**
 * 确保指定资源类型的缓存目录存在，如果不存在则创建
 * @param type 资源类型
 */
export async function ensureCacheDirExists(type: ResourceCacheType): Promise<void> {
  const typeDir = path.join(CACHE_BASE_DIR, type)
  await fsPromises.mkdir(typeDir, { recursive: true })
}

/**
 * 根据资源类型、哈希值和文件扩展名生成本地存储路径
 * @param type 资源类型
 * @param key 资源的唯一 key (hash)
 * @param ext 文件扩展名 (不含点，例如 'png', 'mp3')
 * @returns 完整的本地文件路径
 */
export function getLocalFilePath(type: ResourceCacheType, key: string, ext?: string): string {
  return path.join(
    CACHE_BASE_DIR,
    type,
    key + (ext ? `.${ext}` : '')
  )
}

/**
 * 检查文件是否存在并可访问
 * @param filePath 文件路径
 * @returns Promise<boolean> 是否存在
 */
export async function checkFileExists(filePath: string): Promise<boolean> {
  try {
    await fsPromises.access(filePath)
    return true
  } catch (error: any) {
    if (error.code === 'ENOENT') {
      // 文件或目录不存在
      return false
    }
    // 其他错误，例如权限问题等，可以根据需求处理
    console.error(`Error checking file existence for ${filePath}:`, error)
    throw error // 重新抛出其他类型的错误
  }
}
