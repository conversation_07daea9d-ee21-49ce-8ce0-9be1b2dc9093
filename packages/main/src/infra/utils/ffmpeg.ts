import { join } from 'path'
import { platform } from 'os'
import { app } from 'electron'
import { existsSync } from 'fs'
import { Logger } from '@nestjs/common'
import ffmpeg from 'fluent-ffmpeg'

const logger = new Logger('FfmpegUtils')

/**
 * 获取应用资源目录路径
 * 在开发环境中，资源目录位于项目根目录的 resources 文件夹
 * 在生产环境中，资源目录位于应用包内的 resources 文件夹
 */
function getResourcesPath(): string {
  if (app.isPackaged) {
    // 生产环境：使用 app.getPath('exe') 获取可执行文件目录
    // 然后向上查找 resources 目录
    return join(process.resourcesPath)
  } else {
    // 开发环境：使用项目根目录的 resources 文件夹
    // `app.getAppPath()` 返回项目根目录
    return join(app.getAppPath(), 'resources')
  }
}

/**
 * 根据当前平台获取 ffmpeg 可执行文件的路径
 */
export function getFFmpegPath(): string {
  const resourcesPath = getResourcesPath()
  const currentPlatform = platform()

  let ffmpegFileName: string

  switch (currentPlatform) {
    case 'win32':
      ffmpegFileName = 'ffmpeg.exe'
      break
    case 'darwin':
      ffmpegFileName = 'ffmpeg'
      break
    default:
      throw new Error(`不支持的平台: ${currentPlatform}`)
  }

  const ffmpegPath = join(resourcesPath, ffmpegFileName)

  if (!existsSync(ffmpegPath)) {
    logger.error(`FFmpeg 可执行文件不存在: ${ffmpegPath}`)
    throw new Error(`FFmpeg 可执行文件不存在: ${ffmpegPath}`)
  }

  return ffmpegPath
}

/**
 * 根据当前平台获取 ffprobe 可执行文件的路径
 */
export function getFFprobePath(): string {
  const resourcesPath = getResourcesPath()
  const currentPlatform = platform()

  let ffprobeFileName: string

  switch (currentPlatform) {
    case 'win32':
      ffprobeFileName = 'ffprobe.exe'
      break
    case 'darwin':
      ffprobeFileName = 'ffprobe'
      break
    default:
      throw new Error(`不支持的平台: ${currentPlatform}`)
  }

  const ffprobePath = join(resourcesPath, ffprobeFileName)

  if (!existsSync(ffprobePath)) {
    logger.error(`FFprobe 可执行文件不存在: ${ffprobePath}`)
    throw new Error(`FFprobe 可执行文件不存在: ${ffprobePath}`)
  }

  return ffprobePath
}

/**
 * 配置 fluent-ffmpeg 使用项目捆绑的 ffmpeg 和 ffprobe
 * 这个函数应该在应用启动时调用一次
 */
export function configureFluentFFmpeg(): void {
  try {
    const ffmpegPath = getFFmpegPath()
    const ffprobePath = getFFprobePath()

    // 设置 fluent-ffmpeg 使用的 ffmpeg 和 ffprobe 路径
    ffmpeg.setFfmpegPath(ffmpegPath)
    ffmpeg.setFfprobePath(ffprobePath)

    logger.log('FFmpeg 工具配置完成')
    logger.debug(`FFmpeg 路径: ${ffmpegPath}`)
    logger.debug(`FFprobe 路径: ${ffprobePath}`)
  } catch (error) {
    logger.error('配置 FFmpeg 工具失败:', error)
    throw error
  }
}

/**
 * 获取配置好的 fluent-ffmpeg 实例
 * 使用这个函数替代直接导入 fluent-ffmpeg
 */
export function getConfiguredFFmpeg() {
  return ffmpeg
}
