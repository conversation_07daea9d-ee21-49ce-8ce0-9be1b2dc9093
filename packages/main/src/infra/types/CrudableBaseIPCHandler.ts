import { BaseIPCHandler } from './BaseIPCHandler.js'
import IPCClients from '@app/shared/types/ipc-clients.js'
import { CrudableIpcClient } from '@app/shared/infra/crudable-ipc-client.js'
import { BaseEntity } from '@app/shared/types/local-db/base.js'
import { CrudableBaseService } from '@/infra/types/CrudableBaseService.js'
import { PaginationParams } from '@app/shared/infra/request.js'

/**
 * IPC处理器错误类
 */
export class IPCHandlerError extends Error {

  constructor(message: string, public readonly cause?: unknown) {
    super(message)
    this.name = 'IPCHandlerError'
  }
}

type CrudableClientKey<TClient> = {
  [Key in keyof TClient]: TClient[Key] extends CrudableIpcClient<any, any, any, any, any> ? Key : never
}[keyof TClient]

/**
 * 通用IPC处理基类
 * 提供基本的CRUD操作和查询功能的IPC处理
 */
export abstract class CrudableBaseIPCHandler<
  IPCKey extends Crudable<PERSON>lientKey<IPCClients>,
  TCrudableClient = IPCClients[IPCKey],
  TEntity = TCrudableClient extends CrudableIpcClient<infer FEntity, any, any, any, any>
    ? FEntity
    : never,
  TCreate = TCrudableClient extends CrudableIpcClient<any, infer FCreate, any, any, any>
    ? FCreate
    : never,
  TQuery = TCrudableClient extends CrudableIpcClient<any, any, infer FQuery, any, any>
    ? FQuery
    : never,
  TUpdate = TCrudableClient extends CrudableIpcClient<any, any, any, infer FUpdate, any>
    ? FUpdate
    : never,
  TService extends CrudableBaseService<TEntity & BaseEntity, TCreate, TUpdate, TQuery> = CrudableBaseService<TEntity & BaseEntity, TCreate, TUpdate, TQuery>
> extends BaseIPCHandler<IPCKey> {

  /**
     * 构造函数
     * @param service 服务实例
     * @param resourceName 资源名称，用于构建IPC通道名
     */
  constructor(
    protected service: TService,
    protected resourceName: IPCKey,
  ) {
    super()

    if (!service) {
      throw new IPCHandlerError('服务实例不能为空')
    }

    if (!resourceName) {
      throw new IPCHandlerError('资源名称不能为空')
    }
  }

  /**
     * 注册所有IPC处理程序
     */
  registerAll(): void {
    // 创建记录
    this.registerHandler('create', async (params: TCreate) => {
      if (params === undefined || params === null) {
        throw new IPCHandlerError('创建参数不能为空')
      }
      return this.service.create(params)
    })

    // 获取记录
    this.registerHandler('get', async data => {
      if (!data) {
        throw new IPCHandlerError('获取参数不能为空')
      }
      if (data.id === undefined || data.id === null) {
        throw new IPCHandlerError('ID不能为空')
      }
      return this.service.findById(data.id, data.includeDeleted)
    })

    // 查询记录列表
    this.registerHandler('list', async (data: { params: TQuery, pagination?: PaginationParams }) => {
      if (!data) {
        throw new IPCHandlerError('查询参数不能为空')
      }
      return this.service.findAll(data.params, data.pagination || {})
    })

    // 更新记录
    this.registerHandler('update', async (data: { id: number, params: TUpdate }) => {
      if (!data) {
        throw new IPCHandlerError('更新参数不能为空')
      }
      if (data.id === undefined || data.id === null) {
        throw new IPCHandlerError('ID不能为空')
      }
      if (data.params === undefined || data.params === null) {
        throw new IPCHandlerError('更新内容不能为空')
      }
      return this.service.update(data.id, data.params)
    })

    // 删除记录
    this.registerHandler('delete', async id => {
      if (id === undefined || id === null) {
        throw new IPCHandlerError('ID不能为空')
      }
      return this.service.delete(id)
    })

    // 永久删除记录
    this.registerHandler('permanentlyDelete', async id => {
      if (id === undefined || id === null) {
        throw new IPCHandlerError('ID不能为空')
      }
      return this.service.delete(id)
    })

    // 批量删除记录
    this.registerHandler('batchDelete', async ids => {
      if (!ids || !Array.isArray(ids)) {
        throw new IPCHandlerError('批量删除ID列表必须是数组')
      }
      return this.service.batchDelete(ids)
    })

    // 批量永久删除记录
    this.registerHandler('batchPermanentlyDelete', async ids => {
      if (!ids || !Array.isArray(ids)) {
        throw new IPCHandlerError('批量永久删除ID列表必须是数组')
      }
      return this.service.batchDelete(ids)
    })

    // 注册额外的处理程序
    this.registerExtraHandlers()
  }

  /**
     * 注册额外的IPC处理程序
     * 子类可以重写此方法以添加特定的处理程序
     */
  protected registerExtraHandlers(): void {
    // 子类可以重写此方法以添加特定的处理程序
  }

  /**
     * 平台前缀
     * 使用资源名称作为平台前缀
     */
  protected get platformPrefix() {
    return this.resourceName
  }
}
