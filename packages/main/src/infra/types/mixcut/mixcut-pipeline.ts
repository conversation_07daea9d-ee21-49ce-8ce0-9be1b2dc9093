import { Overlay } from '@app/shared/types/overlay.js'

export abstract class MixcutPipelineProcessor {

  abstract process(overlays: Overlay[]): Promise<Overlay[]>
}

export abstract class SingleOverlayProcessor<TLimitedOverlay extends Overlay = Overlay> {

  abstract process(overlays: TLimitedOverlay): Promise<TLimitedOverlay>
}

export abstract class TargetedOverlayProcessingPipeline extends MixcutPipelineProcessor {

  protected constructor(private readonly processor: SingleOverlayProcessor) {
    super()
  }

  process(overlays: Overlay[]): Promise<Overlay[]> {
    const targetOverlays = this.filterTargetOverlays(overlays)
    return Promise.all(targetOverlays.map(overlay => this.processor.process(overlay)))
  }

  abstract filterTargetOverlays(overlays: Overlay[]): Overlay[]
}
