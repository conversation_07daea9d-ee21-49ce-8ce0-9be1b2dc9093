import { Inject, Injectable } from '@nestjs/common'
import { UploadTaskModel } from '@/infra/models/UploadTaskModel.js'
import { BaseRepository } from '@/infra/types/BaseRepository.js'
import { NestDatabaseService } from '@/modules/global/database.service.js'
import { UploadTask } from '@app/shared/types/local-db/entities/upload-task.entity.js'

/**
 * 上传任务仓储类
 */
@Injectable()
export class UploadTaskRepository extends BaseRepository<
  UploadTaskModel,
  UploadTask.CreateParams,
  UploadTask.UpdateParams,
  UploadTask.QueryParams
> {

  /**
   * 表名
   */
  protected readonly tableName = 'upload_task'

  /**
   * 主键字段名
   */
  protected readonly primaryKey = 'id'

  /**
   * 可排序字段列表
   */
  protected readonly sortableFields = [
    'id', 'name', 'status', 'progress', 'size',
    'updated_at', 'created_at'
  ]

  /**
   * 可查询字段列表
   */
  protected readonly queryableFields = [
    'id', 'team_id', 'uid', 'status', 'type', 'folder_id', 'deleted_at'
  ]

  constructor(
    @Inject(NestDatabaseService)
    protected readonly databaseService: NestDatabaseService
  ) {
    super(databaseService)
  }

  /**
   * 将数据库记录转换为模型
   */
  protected toModel(data: Record<string, any> | null): UploadTaskModel | null {
    if (!data) return null
    return new UploadTaskModel(data)
  }

  /**
   * 获取用户上传任务
   */
  findUserTasks(uid: string, status?: UploadTask.Status, teamId?: number | null): UploadTaskModel[] {
    return super.queryAll({
      uid,
      status,
      team_id: teamId
    })

    // let sql = `
    //   SELECT * FROM ${this.tableName}
    //   WHERE uid = ? AND deleted_at = 0
    // `
    // const params: any[] = [uid]
    //
    // if (status !== undefined) {
    //   sql += ' AND status = ?'
    //   params.push(status)
    // }
    //
    // if (teamId !== undefined && teamId !== null) {
    //   sql += ' AND team_id = ?'
    //   params.push(teamId)
    // }
    //
    // sql += ' ORDER BY created_at DESC'
    //
    // const rows = this.db.prepare(sql).all(...params) as Record<string, any>[]
    // return rows.map(row => this.toModel(row)).filter((model): model is UploadTaskModel => model !== null)
  }

  /**
   * 获取文件夹下的上传任务
   */
  findTasksByFolder(folderId: string, uid: string, teamId?: number | null): UploadTaskModel[] {
    return super.queryAll({
      folder_id: folderId,
      uid,
      team_id: teamId
    })

    // let sql = `
    //   SELECT * FROM ${this.tableName}
    //   WHERE folder_id = ? AND uid = ? AND deleted_at = 0
    // `
    // const params: any[] = [folderId, uid]
    //
    // if (teamId !== undefined && teamId !== null) {
    //   sql += ' AND team_id = ?'
    //   params.push(teamId)
    // }
    //
    // sql += ' ORDER BY created_at DESC'
    //
    // const rows = this.db.prepare(sql).all(...params) as Record<string, any>[]
    // return rows.map(row => this.toModel(row)).filter((model): model is UploadTaskModel => model !== null)
  }

  /**
   * 根据状态获取任务
   */
  findTasksByStatus(status: UploadTask.Status, uid?: string, teamId?: number | null): UploadTaskModel[] {
    return super.queryAll({
      status,
      uid,
      team_id: teamId
    })

    // let sql = `
    //   SELECT * FROM ${this.tableName}
    //   WHERE status = ? AND deleted_at = 0
    // `
    // const params: any[] = [status]
    //
    // if (uid) {
    //   sql += ' AND uid = ?'
    //   params.push(uid)
    // }
    //
    // if (teamId !== undefined && teamId !== null) {
    //   sql += ' AND team_id = ?'
    //   params.push(teamId)
    // }
    //
    // sql += ' ORDER BY created_at DESC'
    //
    // const rows = this.db.prepare(sql).all(...params) as Record<string, any>[]
    // return rows.map(row => this.toModel(row)).filter((model): model is UploadTaskModel => model !== null)
  }

  /**
   * 搜索任务
   */
  search(keyword: string, uid: string, teamId?: number | null): UploadTaskModel[] {
    return super.searchByFields(keyword, ['name'], {
      uid,
      team_id: teamId
    })

    // let sql = `
    //   SELECT * FROM ${this.tableName}
    //   WHERE name LIKE ? AND uid = ? AND deleted_at = 0
    // `
    // const params: any[] = [`%${keyword}%`, uid]
    //
    // if (teamId !== undefined && teamId !== null) {
    //   sql += ' AND team_id = ?'
    //   params.push(teamId)
    // }
    //
    // sql += ' ORDER BY created_at DESC'
    //
    // const rows = this.db.prepare(sql).all(...params) as Record<string, any>[]
    // return rows.map(row => this.toModel(row)).filter((model): model is UploadTaskModel => model !== null)
  }

  /**
   * 获取任务统计
   */
  getTaskStats(uid: string, teamId?: number | null): UploadTask.StatsResult {
    let sql = `
      SELECT COUNT(*)                                                                 as total_count,
             SUM(CASE WHEN status = ${UploadTask.Status.PENDING} THEN 1 ELSE 0 END)   as pending_count,
             SUM(CASE WHEN status = ${UploadTask.Status.UPLOADING} THEN 1 ELSE 0 END) as uploading_count,
             SUM(CASE WHEN status = ${UploadTask.Status.PAUSED} THEN 1 ELSE 0 END)    as paused_count,
             SUM(CASE WHEN status = ${UploadTask.Status.COMPLETED} THEN 1 ELSE 0 END) as completed_count,
             SUM(CASE WHEN status = ${UploadTask.Status.FAILED} THEN 1 ELSE 0 END)    as failed_count,
             SUM(CASE WHEN status = ${UploadTask.Status.CANCELLED} THEN 1 ELSE 0 END) as cancelled_count,
             SUM(size)                                                                as total_size,
             SUM(ROUND(size * progress))                                              as uploaded_size
      FROM ${this.tableName}
      WHERE uid = ?
        AND deleted_at = 0
    `
    const params: any[] = [uid]

    if (teamId !== undefined && teamId !== null) {
      sql += ' AND team_id = ?'
      params.push(teamId)
    }

    const stats = this.db.prepare(sql).get(...params) as Record<string, any>

    // 获取类型分布
    let typeSql = `
      SELECT type, COUNT(*) as count
      FROM ${this.tableName}
      WHERE uid = ? AND deleted_at = 0
    `
    const typeParams: any[] = [uid]

    if (teamId !== undefined && teamId !== null) {
      typeSql += ' AND team_id = ?'
      typeParams.push(teamId)
    }

    typeSql += ' GROUP BY type'

    const typeRows = this.db.prepare(typeSql).all(...typeParams) as Record<string, any>[]
    const typeDistribution: Record<UploadTask.Type, number> = {} as any

    typeRows.forEach(row => {
      typeDistribution[row.type as UploadTask.Type] = row.count
    })

    return {
      total_count: stats.total_count || 0,
      pending_count: stats.pending_count || 0,
      uploading_count: stats.uploading_count || 0,
      paused_count: stats.paused_count || 0,
      completed_count: stats.completed_count || 0,
      failed_count: stats.failed_count || 0,
      cancelled_count: stats.cancelled_count || 0,
      total_size: stats.total_size || 0,
      uploaded_size: stats.uploaded_size || 0,
      type_distribution: typeDistribution
    }
  }

  /**
   * 批量更新任务状态
   */
  batchUpdateStatus(ids: number[], status: UploadTask.Status, reason?: string): number {
    if (ids.length === 0) return 0

    const placeholders = ids.map(() => '?').join(',')
    let sql = `
      UPDATE ${this.tableName}
      SET status     = ?,
          updated_at = ?
    `
    const params: any[] = [status, Date.now()]

    if (reason) {
      sql += ', reason = ?'
      params.push(reason)
    }

    sql += ` WHERE id IN (${placeholders})`
    params.push(...ids)

    const result = this.db.prepare(sql).run(...params)
    return result.changes
  }

  /**
   * 重置上传中的任务为等待状态（应用重启时调用）
   */
  resetUploadingTasks(): number {
    const sql = `
      UPDATE ${this.tableName}
      SET status     = ?,
          reason     = '',
          updated_at = ?
      WHERE status = ?
        AND deleted_at = 0
    `
    const result = this.db.prepare(sql).run(
      UploadTask.Status.PAUSED,
      Date.now(),
      UploadTask.Status.UPLOADING
    )
    return result.changes
  }

  /**
   * 清理已完成的任务
   */
  cleanupCompleted(uid: string, teamId?: number | null): number {
    let sql = `
      DELETE
      FROM ${this.tableName}
      WHERE uid = ?
        AND status = ?
    `
    const params: any[] = [uid, UploadTask.Status.COMPLETED]

    if (teamId) {
      sql += ' AND team_id = ?'
      params.push(teamId)
    }

    const result = this.db.prepare(sql).run(...params)
    return result.changes
  }

  // 查找最大的batch_id
  getMaxBatchId(): number {
    const sql = `
      SELECT MAX(batch_id) as maxBatchId
      FROM ${this.tableName}
    `

    const result = this.db.prepare(sql).get() as { maxBatchId?: number }
    return result?.maxBatchId ?? 1
  }

  findTasksByBatchId(batchId?: number): UploadTask.IUploadTask[] {
    const sql = `
      SELECT *
      FROM ${this.tableName}
      WHERE batch_id = ?
    `
    const params: any[] = [batchId]

    const rows = this.db.prepare(sql).all(...params) as Record<string, any>[]
    const models = rows.map(row => this.toModel(row)).filter((model): model is UploadTaskModel => model !== null)

    return models.map(model => model.toJSON())
  }
}
