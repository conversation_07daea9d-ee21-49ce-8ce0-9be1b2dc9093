import {  statSync, existsSync } from 'fs'
import OSS, { MultipartUploadOptions } from 'ali-oss'
import { Logger } from '@nestjs/common'
import {
  WorkerTaskData,
  WorkerMessage,
  CallbackConfig,
  CallbackVarConfig,
  STSSignature
} from './types.js'

class UploadWorker {

  private activeUploads: Map<number, {
    isPaused: boolean
    ossClient?: OSS
    checkpoint?: any
    uploadPromise?: Promise<any>
  }> = new Map()

  private progressThrottlers: Map<number, {
    lastReportTime: number
    lastProgress: number
  }> = new Map()

  private readonly logger = new Logger('child_process')

  private readonly PROGRESS_REPORT_INTERVAL = 500

  constructor() {
    this.setupMessageHandler()
  }

  private setupMessageHandler(): void {
    if (!process.send) {
      throw new Error('Worker必须在child_process环境中运行')
    }

    process.on('message', async (message: WorkerMessage) => {
      try {
        await this.handleMessage(message)
      } catch (error) {
        this.logger.error(' 处理消息时出错:', error)
        this.sendMessage('error', message.taskId || 0, {
          error: error instanceof Error ? error.message : '未知错误'
        })
      }
    })

    // 处理进程退出信号
    process.on('SIGTERM', () => {
      this.logger.log(' 收到SIGTERM信号，正在清理资源...')
      this.cleanup()
      process.exit(0)
    })

    process.on('SIGINT', () => {
      this.logger.log(' 收到SIGINT信号，正在清理资源...')
      this.cleanup()
      process.exit(0)
    })
  }

  private cleanup(): void {
    // 取消所有活跃的上传任务
    for (const [taskId, uploadInfo] of this.activeUploads) {
      if (uploadInfo.uploadPromise) {
        this.logger.log(` 取消任务 ${taskId}`)
        // 这里可以添加更复杂的取消逻辑
      }
    }
    this.activeUploads.clear()
    this.progressThrottlers.clear()
  }

  private async handleMessage(message: WorkerMessage): Promise<void> {
    const { type, taskId, data } = message

    switch (type) {
      case 'upload':
        await this.handleUpload(data as WorkerTaskData)
        break
      case 'pause':
        this.handlePause(taskId!)
        break
      case 'resume':
        this.handleResume(taskId!)
        break
      case 'cancel':
        this.handleCancel(taskId!)
        break
      default:
        this.logger.warn(` 未知的消息类型: ${type}`)
    }
  }

  private decodeBase64Json<T>(base64Str: string, name: string): T {
    try {
      if (!base64Str || base64Str.trim() === '') {
        throw new Error(`${name}参数为空`)
      }

      const decoded = Buffer.from(base64Str, 'base64').toString('utf-8')
      return JSON.parse(decoded) as T
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : '未知错误'
      this.logger.error(` 解密${name}失败:`, errorMsg)
      throw new Error(`解密${name}配置失败: ${errorMsg}`)
    }
  }

  private async handleUpload(taskData: WorkerTaskData): Promise<void> {
    const { taskId, filePath, fileName, uploadConfig } = taskData

    try {
      this.logger.log(` 开始处理任务 ${taskId}: ${fileName}`)

      if (!this.validateFilePath(filePath)) {
        throw new Error('文件路径不安全')
      }

      if (!this.fileExists(filePath)) {
        throw new Error('文件不存在或无法访问')
      }

      const fileStats = statSync(filePath)
      const fileSize = fileStats.size

      const fileMd5 = uploadConfig.fileMd5 || ''

      const signature = await this.getOSSSignature({
        fileName,
        folderUuid: uploadConfig.folderUuid,
        fileMd5,
        module: uploadConfig.module
      })

      if (!signature) {
        throw new Error('STS签名参数为空')
      }

      let callbackConfig: CallbackConfig
      let callbackVarConfig: CallbackVarConfig

      try {
        callbackConfig = this.decodeBase64Json(signature.callback, 'callback')
        callbackVarConfig = this.decodeBase64Json(signature.callbackVar, 'callbackVar')

        // console.log('[FileUploaderService] 解密后的callback配置:', callbackConfig)
        // console.log('[FileUploaderService] 解密后的callbackVar配置:', callbackVarConfig)
      } catch (error) {
        throw new Error(`解密callback配置失败: ${error instanceof Error ? error.message : '未知错误'}`)
      }

      const ossOptions: MultipartUploadOptions = {
        callback: {
          url: callbackConfig.callbackUrl,
          body: callbackConfig.callbackBody,
          contentType: callbackConfig.callbackBodyType,
          customValue: callbackVarConfig
        }
      }

      const ossClient = this.createOSSClient(signature)
      const progressCallback = this.createProgressCallback(taskId, fileSize)

      // 保存上传状态，包括 OSS 客户端
      this.activeUploads.set(taskId, {
        isPaused: false,
        ossClient,
        checkpoint: undefined
      })

      // 检查是否有 checkpoint 数据（断点续传）
      const existingCheckpoint = uploadConfig.checkpointData ? JSON.parse(uploadConfig.checkpointData) : undefined

      // 开始上传（断点续传）
      const uploadOptions: any = {
        ...ossOptions,
        progress: progressCallback,
        partSize: uploadConfig.partSize,
      }

      // 如果有 checkpoint 数据，添加到上传选项中
      if (existingCheckpoint) {
        uploadOptions.checkpoint = existingCheckpoint
        this.logger.log(` 任务 ${taskId} 使用断点续传`)
      }

      const uploadPromise = ossClient.multipartUpload(signature.objectKey, filePath, uploadOptions)

      // 保存上传 Promise
      const upload = this.activeUploads.get(taskId)!
      upload.uploadPromise = uploadPromise

      const result = await uploadPromise

      const url = new URL((result.res as any)?.requestUrls?.[0])
      const fileUrl = `${url.protocol}//${url.host}/${url.pathname}`

      this.sendMessage('complete', taskId, {
        url: fileUrl,
        fileName: fileName,
        objectId: signature.objectId
      })

      this.logger.log(` 任务 ${taskId} 上传完成`)
    } catch (error) {
      const upload = this.activeUploads.get(taskId)

      const errorMessage = error instanceof Error ? error.message : String(error)
      const isUploadPaused = errorMessage.includes('UPLOAD_PAUSED') ||
                            (upload?.isPaused && errorMessage.includes('Failed to upload some parts'))

      if (isUploadPaused) {
        this.logger.log(` 任务 ${taskId} 因暂停而中断`)
        // 不再发送暂停消息，因为在 handlePause 中已经发送了
        // 这里只是静默处理暂停导致的错误
      } else {
        this.logger.error(` 任务 ${taskId} 上传失败:`, error)
        this.sendMessage('error', taskId, {
          error: error instanceof Error ? error.message : '上传失败'
        })
      }
    } finally {
      // 只有在非暂停状态下才清理上传状态
      const upload = this.activeUploads.get(taskId)
      if (!upload?.isPaused) {
        this.activeUploads.delete(taskId)
        this.progressThrottlers.delete(taskId)
      }
    }
  }

  private handlePause(taskId: number): void {
    const upload = this.activeUploads.get(taskId)

    if (upload) {
      this.logger.log(` 暂停任务 ${taskId}`)

      // 设置暂停标志
      upload.isPaused = true

      // 立即发送暂停消息，不等待 progress 回调
      this.sendMessage('paused', taskId, {
        checkpoint: upload.checkpoint
      })

      this.logger.log(` 任务 ${taskId} 暂停消息已发送`)
    } else {
      this.logger.warn(` 任务 ${taskId} 不存在，无法暂停`)
    }
  }

  private async handleResume(taskId: number): Promise<void> {
    const upload = this.activeUploads.get(taskId)
    if (upload && upload.isPaused) {
      this.logger.log(` 恢复任务 ${taskId}`)

      // 重置暂停标志
      upload.isPaused = false

      // 发送恢复消息，队列管理器会重新启动上传任务
      this.sendMessage('resumed', taskId, {
        checkpoint: upload.checkpoint
      })

      this.logger.log(` 任务 ${taskId} 恢复标志已设置，等待重新启动`)
    } else {
      this.logger.warn(` 任务 ${taskId} 不存在或未暂停，无法恢复`)
    }
  }

  private handleCancel(taskId: number): void {
    const upload = this.activeUploads.get(taskId)
    if (upload) {
      // 设置暂停标志来中断上传
      upload.isPaused = true
      this.activeUploads.delete(taskId)
      this.progressThrottlers.delete(taskId)
      this.logger.log(` 任务 ${taskId} 已取消`)
    }
  }

  private createProgressCallback(taskId: number, fileSize: number) {
    return (progress: number, checkpoint: any) => {
      const upload = this.activeUploads.get(taskId)

      // 保存 checkpoint 数据用于断点续传
      if (upload && checkpoint) {
        upload.checkpoint = checkpoint
      }

      // 检查暂停标志，如果已暂停则抛出错误中断上传
      if (upload?.isPaused) {
        this.logger.log(` 任务 ${taskId} 已暂停，中断上传`)
        throw new Error('UPLOAD_PAUSED')
      }

      const throttler = this.progressThrottlers.get(taskId) || {
        lastReportTime: 0,
        lastProgress: 0
      }

      const now = Date.now()
      const progressDiff = Math.abs(progress - throttler.lastProgress)

      if (now - throttler.lastReportTime >= this.PROGRESS_REPORT_INTERVAL || progressDiff >= 0.01) {
        this.logger.log(` 发送进度更新: 任务${taskId}, 进度${(progress * 100).toFixed(1)}%`)
        this.sendMessage('progress', taskId, {
          progress,
          checkpoint,
          uploadedBytes: Math.round(progress * fileSize)
        })

        throttler.lastReportTime = now
        throttler.lastProgress = progress
        this.progressThrottlers.set(taskId, throttler)
      }
    }
  }

  private async getOSSSignature(params: {
    fileName: string
    folderUuid?: string
    fileMd5: string
    module: string
  }): Promise<STSSignature> {
    try {
      const requestData = {
        fileName: params.fileName,
        folderUuid: params.folderUuid || '',
        fileMd5: params.fileMd5,
        module: params.module
      }

      return new Promise((resolve, reject) => {
        const requestId = Date.now().toString()

        const timeout = setTimeout(() => {
          reject(new Error('获取OSS签名超时'))
        }, 30000)

        const messageHandler = (message: any) => {
          if (message.type === 'oss-signature-response' && message.requestId === requestId) {
            clearTimeout(timeout)
            process.off('message', messageHandler)

            if (message.success) {
              resolve(message.data)
            } else {
              reject(new Error(message.error || '获取OSS签名失败'))
            }
          }
        }

        process.on('message', messageHandler)
        if (process.send) {
          process.send({
            type: 'request-oss-signature',
            requestId,
            data: requestData
          })
        } else {
          reject(new Error('Worker未正确初始化'))
        }
      })
    } catch (error) {
      throw new Error(`获取OSS签名失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  private createOSSClient(signature: STSSignature): OSS {
    return new OSS({
      region: 'oss-cn-hangzhou',
      accessKeyId: signature.accessKeyId,
      accessKeySecret: signature.secretAccessKey,
      stsToken: signature.securityToken,
      bucket: signature.bucket,
      endpoint: signature.endpoint
    })
  }

  private sendMessage(type: string, taskId: number, data: any): void {
    if (process.send) {
      process.send({
        type,
        taskId,
        data
      })
    }
  }

  private fileExists(filePath: string): boolean {
    try {
      return existsSync(filePath)
    } catch {
      return false
    }
  }

  /**
   * 验证文件路径安全性，防止路径遍历攻击
   */
  private validateFilePath(filePath: string): boolean {
    try {
      // 检查路径是否包含危险字符
      const dangerousPatterns = ['../', '..\\', '/etc/', '/root/', 'C:\\Windows\\', 'C:\\System32\\']
      if (dangerousPatterns.some(pattern => filePath.includes(pattern))) {
        this.logger.warn(` 检测到危险路径模式: ${filePath}`)
        return false
      }

      return true
    } catch (error) {
      this.logger.error(` 路径验证失败: ${error}`)
      return false
    }
  }
}

new UploadWorker()
