import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common'
import { dialog } from 'electron'
import { readdirSync, statSync } from 'fs'
import { basename, extname, join, relative } from 'path'
import { UploadTaskModel } from '@/infra/models/UploadTaskModel.js'
import { UploadTaskRepository } from './upload-task.repository.js'
import { CrudableBaseService } from '@/infra/types/CrudableBaseService.js'

import { UploadQueueManager } from './upload-queue-manager.js'
import {
  FolderUploadParams,
  SelectFilesParams,
  StorageFolderUploadParams,
  TeamFolderUploadParams,
  UploadTaskIPCClientUniqueUtilities,
  UploadTaskResponse
} from '@app/shared/types/ipc/crudables/upload-task.js'
import { IPCHandlerError } from '@/infra/types/CrudableBaseIPCHandler.js'
import { createFileFilters, getFileTypeByExt } from '@app/shared/file.js'
import { RequestService } from '@/modules/global/request.service.js'
import { UploadModule } from '@app/shared/types/ipc/file-uploader.js'
import { UploadTask } from '@app/shared/types/local-db/entities/upload-task.entity.js'
import { ResourceTypes } from '@app/shared/types/resource-cache.types.js'

/**
 * 文件夹结构节点
 */
interface FolderNode {
  name: string
  path: string
  relativePath: string
  folderUuid?: string
  children: FolderNode[]
  files: FileNode[],

}

/**
 * 文件节点
 */
interface FileNode {
  name: string
  path: string
  relativePath: string
  size: number
  type: UploadTask.Type
}

interface CreateDirectoryParams {
  folderNode: FolderNode,
  parentFolderUuid: string,
  folderOptions: TeamFolderUploadParams | StorageFolderUploadParams
}

/**
 * 上传任务服务类
 */
@Injectable()
export class UploadTaskService extends CrudableBaseService<
  UploadTaskModel,
  UploadTask.CreateParams,
  UploadTask.UpdateParams,
  UploadTask.QueryParams,
  UploadTaskRepository
> implements UploadTaskIPCClientUniqueUtilities {

  private readonly logger = new Logger('UploadTaskService')

  /**
   * 默认队列配置
   */
  private queueConfig: UploadTask.QueueConfig = {
    max_concurrent_uploads: 3,
    retry_attempts: 3,
    retry_delay: 5000,
    chunk_size: 1024 * 1024 // 1MB
  }

  constructor(
    @Inject(UploadTaskRepository) repository: UploadTaskRepository,
    @Inject(forwardRef(() => UploadQueueManager)) private queueManager: UploadQueueManager,
    @Inject(RequestService) private readonly requestService: RequestService
  ) {
    super(repository)
    this.scheduleInitialization()
  }

  startUpload(data: { id: number }): boolean {
    if (!data) {
      throw new IPCHandlerError('开始上传任务参数不能为空')
    }
    if (data.id === undefined || data.id === null) {
      throw new IPCHandlerError('任务ID不能为空')
    }
    return this.updateTaskStatus(data.id, UploadTask.Status.UPLOADING)
  }

  async pauseUpload(data: { id: number }): Promise<boolean> {
    if (!data) {
      throw new IPCHandlerError('暂停上传任务参数不能为空')
    }
    if (data.id === undefined || data.id === null) {
      throw new IPCHandlerError('任务ID不能为空')
    }

    const { id } = data
    const task = this.repository.findById(id)
    if (!task) {
      throw new IPCHandlerError('任务不存在')
    }

    if (!task.canPause()) {
      throw new IPCHandlerError('任务当前状态不允许暂停')
    }

    // 调用队列管理器的暂停方法，它会处理正在上传的任务或直接更新状态
    if (this.queueManager) {
      this.logger.debug(` 调用队列管理器暂停任务 ${id}`)
      return await this.queueManager.pauseUpload(id)
    }

    // 如果没有队列管理器，直接更新数据库状态
    return this.updateTaskStatus(id, UploadTask.Status.PAUSED)
  }

  async resumeUpload(data: { id: number }): Promise<boolean> {
    if (!data) {
      throw new IPCHandlerError('恢复上传任务参数不能为空')
    }
    if (data.id === undefined || data.id === null) {
      throw new IPCHandlerError('任务ID不能为空')
    }

    const { id } = data
    const task = this.repository.findById(id)
    if (!task) {
      throw new IPCHandlerError('任务不存在')
    }

    if (!task.canResume()) {
      throw new IPCHandlerError('任务当前状态不允许恢复')
    }

    if (this.queueManager) {
      this.logger.debug(` 调用队列管理器恢复任务 ${id}`)
      return await this.queueManager.resumeUpload(id)
    }

    // 如果没有队列管理器，直接更新状态为等待
    return this.updateTaskStatus(id, UploadTask.Status.PENDING)
  }

  async cancelUpload(data: { id: number }): Promise<boolean> {
    if (!data) {
      throw new IPCHandlerError('取消上传任务参数不能为空')
    }
    if (data.id === undefined || data.id === null) {
      throw new IPCHandlerError('任务ID不能为空')
    }

    const { id } = data
    const task = this.repository.findById(id)
    if (!task) {
      throw new IPCHandlerError('任务不存在')
    }

    if (!task.canCancel()) {
      throw new IPCHandlerError('任务当前状态不允许取消')
    }

    // 调用队列管理器的取消方法，它会处理正在上传的任务或直接更新状态
    if (this.queueManager) {
      this.logger.debug(` 调用队列管理器取消任务 ${id}`)
      const cancelled = await this.queueManager.cancelUpload(id)
      if (cancelled) {
        // 如果队列管理器成功取消了任务，更新数据库状态
        return this.updateTaskStatus(id, UploadTask.Status.CANCELLED, '用户取消')
      }
    }

    // 如果没有队列管理器或任务不在队列中，直接更新数据库状态
    return this.updateTaskStatus(id, UploadTask.Status.CANCELLED, '用户取消')
  }

  retryUpload(data: { id: number }): boolean {
    if (!data) {
      throw new IPCHandlerError('重试上传任务参数不能为空')
    }
    if (data.id === undefined || data.id === null) {
      throw new IPCHandlerError('任务ID不能为空')
    }

    const { id } = data
    const task = this.repository.findById(id)
    if (!task) {
      throw new IPCHandlerError('任务不存在')
    }

    if (!task.canRetry()) {
      throw new IPCHandlerError('任务当前状态不允许重试')
    }

    task.reset()
    return this.repository.update(id, {
      progress: task.progress,
      status: task.status,
      reason: task.reason
    })
  }

  batchOperation(data: UploadTask.BatchParams): number {
    if (!data) {
      throw new IPCHandlerError('批量操作参数不能为空')
    }
    if (!data.ids || !Array.isArray(data.ids) || data.ids.length === 0) {
      throw new IPCHandlerError('任务ID数组不能为空')
    }
    if (!data.action) {
      throw new IPCHandlerError('操作类型不能为空')
    }

    switch (data.action) {
      case 'pause': {
        return this.batchUpdateStatus(data.ids, UploadTask.Status.PAUSED)
      }
      case 'resume': {
        return this.batchUpdateStatus(data.ids, UploadTask.Status.PENDING)
      }
      case 'cancel': {
        return this.batchUpdateStatus(data.ids, UploadTask.Status.CANCELLED, '用户取消')
      }
      case 'retry': {
        let retryCount = 0
        for (const id of data.ids) {
          try {
            if (this.retryUpload({ id })) {
              retryCount++
            }
          } catch (error) {
            // 忽略单个任务重试失败
          }
        }
        return retryCount
      }
      case 'delete': {
        return this.batchDelete(data.ids)
      }
      default: {
        throw new IPCHandlerError(`不支持的操作类型: ${data.action}`)
      }
    }
  }

  /**
   * 设置队列管理器引用（由队列管理器调用）
   */
  setQueueManager(queueManager: UploadQueueManager): void {
    this.queueManager = queueManager
  }

  /**
   * 调度初始化，等待数据库准备就绪
   */
  private scheduleInitialization(): void {
    const checkAndInit = () => {
      try {
        // 尝试访问数据库，如果失败则继续等待
        this.repository.resetUploadingTasks()
      } catch (error) {
        // 数据库还未准备好，继续等待
        setTimeout(checkAndInit, 2000)
      }
    }

    // 延迟开始检查
    setTimeout(checkAndInit, 3000)
  }

  /**
   * 获取用户上传任务
   */
  getUserTasks(data: { uid: string, status?: UploadTask.Status, teamId?: number | null }): UploadTaskModel[] {
    if (!data) {
      throw new IPCHandlerError('获取用户上传任务参数不能为空')
    }
    if (!data.uid) {
      throw new IPCHandlerError('用户ID不能为空')
    }

    try {
      const { uid, status, teamId } = data
      return this.repository.findUserTasks(uid, status, teamId)
    } catch (error: any) {
      throw new IPCHandlerError(`获取用户上传任务失败: ${error.message}`)
    }
  }

  /**
   * 获取文件夹下的上传任务
   */
  getTasksByFolder(data: { folderId: string, uid: string, teamId?: number | null }): UploadTaskModel[] {
    if (!data) {
      throw new IPCHandlerError('获取文件夹上传任务参数不能为空')
    }
    if (!data.folderId) {
      throw new IPCHandlerError('文件夹ID不能为空')
    }
    if (!data.uid) {
      throw new IPCHandlerError('用户ID不能为空')
    }
    try {
      const { folderId, uid, teamId } = data
      return this.repository.findTasksByFolder(folderId, uid, teamId)
    } catch (error: any) {
      throw new IPCHandlerError(`获取文件夹上传任务失败: ${error.message}`)
    }
  }

  /**
   * 根据状态获取任务
   */
  getTasksByStatus(status: UploadTask.Status, uid?: string, teamId?: number | null): UploadTaskModel[] {
    try {
      return this.repository.findTasksByStatus(status, uid, teamId)
    } catch (error: any) {
      throw new IPCHandlerError(`根据状态获取上传任务失败: ${error.message}`)
    }
  }

  /**
   * 搜索上传任务
   */
  searchTasks(data: { keyword: string, uid: string, teamId?: number | null }): UploadTaskModel[] {
    if (!data) {
      throw new IPCHandlerError('搜索上传任务参数不能为空')
    }
    if (!data.keyword) {
      throw new IPCHandlerError('搜索关键词不能为空')
    }
    if (!data.uid) {
      throw new IPCHandlerError('用户ID不能为空')
    }

    try {
      const { keyword, uid, teamId } = data
      return this.repository.search(keyword, uid, teamId)
    } catch (error: any) {
      throw new IPCHandlerError(`搜索上传任务失败: ${error.message}`)
    }
  }

  /**
   * 获取任务统计
   */
  getTaskStats(data: { uid: string, teamId?: number | null }): UploadTask.StatsResult {
    if (!data) {
      throw new IPCHandlerError('获取上传任务统计参数不能为空')
    }
    if (!data.uid) {
      throw new IPCHandlerError('用户ID不能为空')
    }

    try {
      const { uid, teamId } = data
      return this.repository.getTaskStats(uid, teamId)
    } catch (error: any) {
      throw new IPCHandlerError(`获取上传任务统计失败: ${error.message}`)
    }
  }

  /**
   * 根据ID获取任务
   */
  findById(id: number): UploadTaskModel | null {
    return this.repository.findById(id)
  }

  /**
   * 更新任务进度
   */
  updateTaskProgress(id: number, progress: number): boolean {
    try {
      const task = this.repository.findById(id)
      if (!task) {
        throw new IPCHandlerError('任务不存在')
      }

      task.updateProgress(progress)
      return this.repository.update(id, {
        progress: task.progress
      })
    } catch (error: any) {
      throw new IPCHandlerError(`更新任务进度失败: ${error.message}`)
    }
  }

  /**
   * 更新任务状态
   */
  updateTaskStatus(id: number, status: UploadTask.Status, reason?: string): boolean {
    try {
      const task = this.repository.findById(id)
      if (!task) {
        throw new IPCHandlerError('任务不存在')
      }

      task.setStatus(status, reason)
      return this.repository.update(id, {
        status: task.status,
        reason: task.reason
      })
    } catch (error: any) {
      throw new IPCHandlerError(`更新任务状态失败: ${error.message}`)
    }
  }

  /**
   * 批量更新任务状态
   */
  batchUpdateStatus(ids: number[], status: UploadTask.Status, reason?: string): number {
    try {
      return this.repository.batchUpdateStatus(ids, status, reason)
    } catch (error: any) {
      throw new IPCHandlerError(`批量更新任务状态失败: ${error.message}`)
    }
  }

  /**
   * 获取队列配置
   */
  getQueueConfig(): UploadTask.QueueConfig {
    return { ...this.queueConfig }
  }

  /**
   * 更新队列配置
   */
  updateQueueConfig(config: Partial<UploadTask.QueueConfig>): boolean {
    if (!config) {
      throw new IPCHandlerError('队列配置不能为空')
    }
    try {
      this.queueConfig = { ...this.queueConfig, ...config }
      return true
    } catch (error: any) {
      throw new IPCHandlerError(`更新队列配置失败: ${error.message}`)
    }
  }

  /**
   * 获取队列状态
   */
  getQueueStatus(): {
    active_count: number
    pending_count: number
    paused_count: number
    max_concurrent: number
    queue_manager?: {
      initialized: boolean
      activeProcesses: number
      activeTasks: number
      maxProcesses: number
      processTaskCounts: Record<string, number>
      progressBatchStatus: {
        currentSize: number
        maxSize: number
        hasTimer: boolean
      }
    }
  } {
    try {
      const uploadingTasks = this.repository.findTasksByStatus(UploadTask.Status.UPLOADING)
      const pendingTasks = this.repository.findTasksByStatus(UploadTask.Status.PENDING)
      const pausedTasks = this.repository.findTasksByStatus(UploadTask.Status.PAUSED)

      const basicStatus = {
        active_count: uploadingTasks.length,
        pending_count: pendingTasks.length,
        paused_count: pausedTasks.length,
        max_concurrent: this.queueConfig.max_concurrent_uploads
      }

      // 如果有队列管理器，获取其详细状态
      if (this.queueManager) {
        try {
          const queueManagerStatus = this.queueManager.getQueueStatus()
          return {
            ...basicStatus,
            queue_manager: queueManagerStatus
          }
        } catch (error) {
          this.logger.warn('获取队列管理器状态失败:', error)
          // 如果获取队列管理器状态失败，只返回基础状态
          return basicStatus
        }
      }

      return basicStatus
    } catch (error: any) {
      throw new IPCHandlerError(`获取队列状态失败: ${error.message}`)
    }
  }

  /**
   * 清理已完成的任务
   */
  cleanupCompleted(data: { uid: string, teamId?: number | null }): number {
    if (!data) {
      throw new IPCHandlerError('清理已完成任务参数不能为空')
    }
    if (!data.uid) {
      throw new IPCHandlerError('用户ID不能为空')
    }

    try {
      const { uid, teamId } = data
      return this.repository.cleanupCompleted(uid, teamId)
    } catch (error: any) {
      throw new IPCHandlerError(`清理已完成任务失败: ${error.message}`)
    }
  }

  /**
   * 选择文件
   */
  async selectFiles(data: SelectFilesParams): Promise<string[]> {
    try {
      const { multiple = false, filters, folder = false } = data
      const defaultFilters = createFileFilters(['VIDEO', 'AUDIO', 'IMAGE'])
      const properties: Electron.OpenDialogOptions['properties'] = []

      if (folder) {
        properties.push('openDirectory')
      } else {
        properties.push('openFile')
      }

      if (multiple) {
        properties.push('multiSelections')
      }

      const result = await dialog.showOpenDialog({
        properties,
        filters: folder ? undefined : (filters || defaultFilters)
      })

      return result.canceled ? [] : result.filePaths
    } catch (error: any) {
      this.logger.error(' 选择文件失败:', error)
      return []
    }
  }

  /**
   * 从本地路径上传文件
   */
  async uploadFromPath(data: { taskIds: number[], maxSize?: number }): Promise<UploadTaskResponse> {
    if (!data) throw new IPCHandlerError('从路径上传文件参数不能为空')
    const {
      taskIds,
      maxSize = UploadTask.DEFAULT_MAX_SIZE
    } = data
    if (taskIds.length === 0) throw new IPCHandlerError('任务ID不能为空')

    const taskResult: UploadTask.IUploadTask[] = []
    const failedTasks: { taskId: number, error: string }[] = []

    const maxBatch = this.repository.getMaxBatchId() ?? 1

    for (const taskId of taskIds) {
      try {
        const task = this.repository.findById(taskId)
        if (!task) {
          failedTasks.push({ taskId, error: '任务不存在' })
          continue
        }

        if (!task.isPending() && !task.isPaused() && !task.isFailed()) {
          failedTasks.push({ taskId, error: '任务当前状态不允许上传' })
          continue
        }

        const filePath = task.local_path
        const fileName = basename(filePath)
        const fileExt = extname(fileName).toLowerCase()
        const fileType = getFileTypeByExt(fileExt)

        const stats = statSync(filePath)
        if (!stats.isFile()) return { success: false, error: '指定路径不是文件' }

        if (stats.size >= maxSize) {
          failedTasks.push({ taskId, error: `文件过大: ${filePath} (${(stats.size / 1024 / 1024).toFixed(2)} MB)` })
          this.repository.update(taskId, {
            status: UploadTask.Status.CANCELLED,
            reason: '文件过大'
          })
          continue
        }

        const fileInfo = await this.queueManager.calculateFileHash(filePath)

        this.repository.update(taskId, {
          local_path: filePath,
          name: fileName,
          hash: fileInfo.md5Content,
          type: fileType,
          size: fileInfo.size,
          status: UploadTask.Status.PENDING,
          batch_id: maxBatch + 1,
        })

        const updatedTask = this.repository.findById(taskId)
        if (updatedTask) taskResult.push(updatedTask)
      } catch (error: any) {
        this.logger.error(`任务 ${taskId} 上传失败:`, error)
        this.updateTaskStatus(taskId, UploadTask.Status.FAILED, error.message || '上传异常')
        failedTasks.push({ taskId, error: error.message || '上传异常' })
      }
    }

    if (failedTasks.length > 0 && taskResult.length === 0) {
      return { success: false, error: failedTasks.map(f => `任务${f.taskId}: ${f.error}`).join('; ') }
    }

    return { success: true, tasks: taskResult }
  }

  /**
   * 递归遍历文件夹结构
   */
  private async traverseFolder(
    folderPath: string,
    basePath: string,
    maxSize: number = UploadTask.DEFAULT_MAX_SIZE
  ): Promise<FolderNode> {
    const stats = statSync(folderPath)

    if (!stats.isDirectory()) {
      throw new IPCHandlerError('指定路径不是文件夹')
    }

    const folderName = basename(folderPath)
    const relativePath = relative(basePath, folderPath)

    const folderNode: FolderNode = {
      name: folderName,
      path: folderPath,
      relativePath: relativePath || folderName,
      children: [],
      files: []
    }

    try {
      const items = readdirSync(folderPath)

      for (const item of items) {
        const itemPath = join(folderPath, item)
        const itemStats = statSync(itemPath)

        if (itemStats.isDirectory()) {
          // 递归处理子文件夹
          const childFolder = await this.traverseFolder(itemPath, basePath, maxSize)
          folderNode.children.push(childFolder)
        } else if (itemStats.isFile()) {
          // 处理文件
          const fileNode = this.createFileNode(itemPath, basePath, itemStats, maxSize)
          if (fileNode) {
            folderNode.files.push(fileNode)
          }
        }
      }
    } catch (error) {
      this.logger.error(`遍历文件夹失败: ${folderPath}`, error)
      throw new IPCHandlerError(`无法读取文件夹: ${folderPath}`)
    }

    return folderNode
  }

  /**
   * 创建文件节点
   */
  private createFileNode(
    filePath: string,
    basePath: string,
    stats: any,
    maxSize: number
  ): FileNode | null {
    const fileName = basename(filePath)
    const relativePath = relative(basePath, filePath)
    const fileExt = extname(fileName).toLowerCase()

    // 检查文件大小
    if (stats.size > maxSize) {
      return null
    }

    const type = getFileTypeByExt(fileExt)

    return {
      name: fileName,
      path: filePath,
      relativePath,
      size: stats.size,
      type
    }
  }

  private async createRemoteDirectory(data: CreateDirectoryParams): Promise<string> {
    const { folderNode, parentFolderUuid, folderOptions } = data
    try {
      // media 类型使用特殊的 URL，其他类型使用统一的模式
      const url = folderOptions.directoryType === 'storage'
        ? '/app-api/creative/storage/directory/create'
        : `/app-api/creative/team/directory/${folderOptions.apiPrefix}/create`

      const response = await this.requestService.post<string>(url, {
        folderName: folderNode.name,
        parentId: parentFolderUuid
      })

      this.logger.debug(`创建远程目录成功: ${folderNode.name} -> ${response}`)
      return response
    } catch (error) {
      this.logger.error(`创建远程目录失败: ${folderNode.name}`, error)
      throw new IPCHandlerError(`创建远程目录失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 批量创建远程目录结构
   */
  private async createRemoteDirectoryStructure(data: CreateDirectoryParams): Promise<void> {
    const { folderNode, parentFolderUuid, folderOptions } = data

    // 为当前文件夹创建远程目录
    folderNode.folderUuid = await this.createRemoteDirectory({
      folderNode,
      parentFolderUuid,
      folderOptions
    }
    )

    // 递归为子文件夹创建远程目录
    for (const childFolder of folderNode.children) {
      await this.createRemoteDirectoryStructure({
        folderNode: childFolder,
        parentFolderUuid: folderNode.folderUuid!,
        folderOptions
      }
      )
    }
  }

  /**
   * 上传文件夹
   */
  async uploadFolder(data: FolderUploadParams): Promise<UploadTaskResponse> {
    if (!data) {
      throw new IPCHandlerError('上传文件夹参数不能为空')
    }

    if (!data?.teamId) {
      throw new IPCHandlerError('TeamId 不能为空')
    }

    const {
      folderPath,
      parentFolderUuid,
      uid,
      teamId,
      maxSize = UploadTask.DEFAULT_MAX_SIZE,
      folderOptions,
      uploadModule,
      library
    } = data

    if (!folderPath) {
      throw new IPCHandlerError('文件夹路径不能为空')
    }
    if (!parentFolderUuid) {
      throw new IPCHandlerError('父文件夹UUID不能为空')
    }
    if (!uid) {
      throw new IPCHandlerError('用户ID不能为空')
    }

    try {
      // 检查文件夹是否存在
      const stats = statSync(folderPath)
      if (!stats.isDirectory()) {
        return { success: false, error: '指定路径不是文件夹' }
      }

      this.logger.debug(`开始上传文件夹: ${folderPath}`)

      // 1. 遍历文件夹结构
      const folderStructure = await this.traverseFolder(folderPath, folderPath, maxSize)

      // 检查是否有有效文件
      const allFiles = this.collectAllFiles(folderStructure)
      if (allFiles.length === 0) {
        return { success: false, error: '文件夹中没有找到支持的文件类型' }
      }

      this.logger.debug('folderStructure', folderStructure)
      this.logger.debug(`找到 ${allFiles.length} 个有效文件`)

      await this.createRemoteDirectoryStructure({
        folderNode: folderStructure,
        parentFolderUuid,
        folderOptions
      })

      const filesWithFolderUuid = this.collectAllFiles(folderStructure)

      this.logger.debug('重新收集文件，确保 folderUuid 正确分配')

      // 5. 为每个文件创建上传任务
      const taskIds = await this.createUploadTasksForFiles({
        files: filesWithFolderUuid,
        uid,
        teamId,
        uploadModule,
        library
      })

      this.logger.debug(`创建了 ${taskIds.length} 个上传任务`)

      const tasks = this.repository.findByIds(taskIds) ?? undefined

      return {
        success: true,
        tasks,
      }
    } catch (error: any) {
      this.logger.error('上传文件夹失败:', error)
      return {
        success: false,
        error: error.message || '上传文件夹失败'
      }
    }
  }

  /**
   * 收集文件夹结构中的所有文件
   */
  private collectAllFiles(folderNode: FolderNode): Array<FileNode & { folderUuid: string }> {
    const files: Array<FileNode & { folderUuid: string }> = []

    // 添加当前文件夹的文件
    for (const file of folderNode.files) {
      files.push({
        ...file,
        folderUuid: folderNode.folderUuid!
      })
    }

    // 递归添加子文件夹的文件
    for (const childFolder of folderNode.children) {
      files.push(...this.collectAllFiles(childFolder))
    }

    return files
  }

  /**
   * 为文件列表创建上传任务
   */
  private async createUploadTasksForFiles(
    data: {
      files: Array<FileNode & { folderUuid: string }>,
      uid: string,
      teamId: number,
      uploadModule: UploadModule,
      library: ResourceTypes
    }
  ): Promise<number[]> {
    const { files, uid, teamId, uploadModule, library } = data
    const taskIds: number[] = []
    const maxBatch = this.repository.getMaxBatchId() ?? 1

    for (const file of files) {
      try {
        // 计算文件哈希值
        this.logger.debug(`计算文件哈希值: ${file.name}`)
        const hashResult = await this.queueManager.calculateFileHash(file.path)

        // 创建上传任务
        const task = await this.repository.create({
          uid,
          team_id: teamId,
          name: file.name,
          local_path: file.path,
          type: file.type,
          folder_id: file.folderUuid,
          upload_module: uploadModule,
          size: hashResult.size,
          hash: hashResult.md5Content,
          batch_id: maxBatch + 1,
          library: library
        })

        taskIds.push(task.id)

        this.logger.debug(`创建上传任务: ${file.name} -> 任务ID: ${task.id}, 哈希: ${hashResult.md5Content}`)
      } catch (error) {
        this.logger.error(`创建上传任务失败: ${file.name}`, error)
        // 继续处理其他文件，不中断整个流程
      }
    }

    return taskIds
  }

  /**
   * 获取最新批次 ID（如果不存在返回 null）
   */
  getMaxBatchId(): number | null {
    try {
      const id = this.repository.getMaxBatchId()
      return id ?? null
    } catch (error: any) {
      this.logger.error('获取最新批次 ID 失败:', error)
      return null
    }
  }

  /**
   * 批次完成判断
   */
  batchTaskComplete(batchId: number): {
    batch_id: number
    isCompleted: boolean
    tasks: UploadTask.IUploadTask[]
  } {
    const tasks = this.repository.findTasksByBatchId(batchId)
    const total = tasks.length
    let success = 0
    let failed = 0
    let cancelled = 0

    for (const t of tasks) {
      if (t.status === UploadTask.Status.COMPLETED) success++
      else if (t.status === UploadTask.Status.FAILED) failed++
      else if (t.status === UploadTask.Status.CANCELLED) cancelled++
    }

    return {
      batch_id: batchId,
      isCompleted: total > 0 && total === (success + failed + cancelled),
      tasks: tasks
    }
  }

  getUploadingTasksByLibrary(data: { library: ResourceTypes }): UploadTask.IUploadTask[] {
    return this.repository.findMany({ library: data.library })
      .filter(task =>
        task.status === UploadTask.Status.PENDING
        || task.status === UploadTask.Status.UPLOADING
      )
  }
}
