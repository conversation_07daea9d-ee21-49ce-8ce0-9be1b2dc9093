import { Modu<PERSON> } from '@nestjs/common'
import { UploadTaskIPCHandler } from './upload-task.ipc-handler.js'
import { UploadTaskRepository } from './upload-task.repository.js'
import { UploadTaskService } from './upload-task.service.js'
import { GlobalModule } from '../../global/global.module.js'
import { UploadQueueManager } from './upload-queue-manager.js'
import { FileUploaderModule } from '../../file-uploader/file-uploader.module.js'

@Module({
  imports: [
    GlobalModule,
    FileUploaderModule
  ],
  providers: [
    UploadTaskRepository,
    UploadTaskService,
    UploadTaskIPCHandler,
    UploadQueueManager,
  ],
  exports: [
    UploadTaskService,
    UploadQueueManager,
  ]
})
export class UploadTaskModule {}
