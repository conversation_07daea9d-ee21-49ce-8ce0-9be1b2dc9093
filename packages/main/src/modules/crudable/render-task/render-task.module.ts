import { Module } from '@nestjs/common'
import { RenderTaskIPCHandler } from './render-task.ipc-handler.js'
import { RenderTaskRepository } from './render-task.repository.js'
import { RenderTaskService } from './render-task.service.js'
import { RenderTaskMonitorService } from './render-task-monitor.service.js'
import { GlobalModule } from '../../global/global.module.js'

@Module({
  imports: [
    GlobalModule
  ],
  providers: [
    RenderTaskRepository,
    RenderTaskService,
    RenderTaskIPCHandler,
    RenderTaskMonitorService,
  ],
  exports: [
    RenderTaskService,
  ]
})
export class RenderTaskModule {}
