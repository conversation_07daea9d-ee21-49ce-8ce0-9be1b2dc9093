import { Inject, Injectable } from '@nestjs/common'
import { RenderTaskService } from './render-task.service.js'
import { CrudableBaseIPCHandler } from '@/infra/types/CrudableBaseIPCHandler.js'

/**
 * 渲染任务IPC处理器
 */
@Injectable()
export class RenderTask<PERSON>CHandler extends CrudableBaseIPCHandler<'renderTask'> {

  constructor(
    @Inject(RenderTaskService) private renderTaskService: RenderTaskService
  ) {
    super(renderTaskService, 'renderTask')
  }

  protected registerExtraHandlers() {
    this.registerHandler('getUserTasks', v => this.renderTaskService.getUserTasks(v))

    this.registerHandler('getTaskStats', v => this.renderTaskService.getTaskStats(v))
    
    this.registerHandler('cleanupCompleted', v => this.renderTaskService.cleanupCompleted(v))
  }
}
