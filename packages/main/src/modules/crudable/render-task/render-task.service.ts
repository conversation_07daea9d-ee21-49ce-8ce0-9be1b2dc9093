import { Inject, Injectable, Logger } from '@nestjs/common'
import { CrudableBaseService } from '@/infra/types/CrudableBaseService.js'
import { RenderTaskModel } from '@/infra/models/RenderTaskModel.js'
import { RenderTaskRepository } from './render-task.repository.js'
import { RenderTask } from '@app/shared/types/local-db/entities/render-task.entity.js'
import { RequestService } from '@/modules/global/request.service.js'
import { RenderTaskIPCClientUniqueUtilities } from '@app/shared/types/ipc/crudables/render-task.js'

/**
 * 渲染任务服务类
 */
@Injectable()
export class RenderTaskService extends CrudableBaseService<
  RenderTaskModel,
  RenderTask.CreateParams,
  RenderTask.UpdateParams,
  RenderTask.QueryParams,
  RenderTaskRepository
> implements RenderTaskIPCClientUniqueUtilities {

  private readonly logger = new Logger(RenderTaskService.name)

  constructor(
    @Inject(RenderTaskRepository) repository: RenderTaskRepository,
    @Inject(RequestService) private readonly requestService: RequestService
  ) {
    super(repository)
  }

  /**
   * 获取用户渲染任务
   */
  public async getUserTasks(params: { uid: string, status?: RenderTask.RenderTaskStatus, teamId?: number | null }): Promise<RenderTaskModel[]> {
    const { uid, status, teamId } = params
    return this.repository.findUserTasks(uid, status, teamId)
  }

  /**
   * 获取任务统计信息
   */
  public async getTaskStats({ uid, teamId, }: { uid: string, teamId?: number | null }): Promise<RenderTask.TaskStats> {
    return this.repository.getTaskStats(uid, teamId)
  }

  /**
   * 清理已完成的任务
   */
  public async cleanupCompleted({ uid, teamId }: { uid: string, teamId?: number | null }): Promise<number> {
    const deletedCount = this.repository.cleanupCompleted(uid, teamId)
    this.logger.debug(`清理已完成任务: ${deletedCount} 个`)
    return deletedCount
  }

  /**
   * 获取需要监听的任务
   */
  public getPendingTasks(): RenderTaskModel[] {
    return this.repository.findPendingTasks()
  }

  /**
   * 批量更新任务进度信息
   */
  public async updateTasksProgress(cloudInfos: RenderTask.CloudTaskInfo[]): Promise<void> {
    for (const cloudInfo of cloudInfos) {
      const task = this.#findByTaskNo(cloudInfo.taskNo)
      if (!task) {
        this.logger.warn(`未找到任务: ${cloudInfo.taskNo}`)
        continue
      }

      const updateParams: RenderTask.UpdateParams = {
        progress: cloudInfo.progress
      }

      if (cloudInfo.status !== task.status) {
        updateParams.status = cloudInfo.status
        if (cloudInfo.status === RenderTask.RenderTaskStatus.RENDERING && task.render_start_time === 0) {
          updateParams.render_start_time = Date.now()
        } else if (
          (cloudInfo.status === RenderTask.RenderTaskStatus.COMPLETED || cloudInfo.status === RenderTask.RenderTaskStatus.FAILED) &&
          task.render_end_time === 0
        ) {
          updateParams.render_end_time = Date.now()
        }
      }

      // 更新错误信息
      if (cloudInfo.errorMsg) {
        updateParams.reason = cloudInfo.errorMsg
      }

      // 更新文件信息
      if (cloudInfo.fileSize) {
        updateParams.file_size = cloudInfo.fileSize
      }
      if (cloudInfo.url) {
        updateParams.download_url = cloudInfo.url
      }

      this.repository.update(task.id, updateParams)
    }
  }

  /**
   * 获取渲染进度（从云端API）
   */
  public async getRenderProgress(taskNos: string[]): Promise<RenderTask.CloudTaskInfo[]> {
    try {
      return this.requestService.post<any[]>('/app-api/creative/render/list', { taskNos })
    } catch (error) {
      this.logger.error('获取渲染进度失败:', error)
      return []
    }
  }

  /**
   * 根据任务编号查找任务
   */
  #findByTaskNo(taskNo: string): RenderTaskModel | null {
    return this.repository.findByTaskNo(taskNo)
  }
}
