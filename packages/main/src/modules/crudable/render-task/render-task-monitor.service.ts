import { Inject, Injectable, Logger, OnModuleDestroy, OnModuleInit } from '@nestjs/common'
import { RenderTaskService } from './render-task.service.js'
import { RenderTask } from '@app/shared/types/local-db/entities/render-task.entity.js'

/**
 * 渲染任务状态监听服务
 * 负责轮询云端API获取渲染进度并更新本地数据
 */
@Injectable()
export class RenderTaskMonitorService implements OnModuleInit, OnModuleDestroy {

  private readonly logger = new Logger('RenderTaskMonitorService')
  private monitorTimer: NodeJS.Timeout | null = null
  private isMonitoring = false

  /**
   * 监听间隔（毫秒）
   */
  private readonly MONITOR_INTERVAL = 10000 // 10秒

  /**
   * 最大监听时长（毫秒）
   */
  private readonly MAX_MONITOR_DURATION = 24 * 60 * 60 * 1000 // 24小时

  constructor(
    @Inject(RenderTaskService) private readonly renderTaskService: RenderTaskService
  ) {}

  /**
   * 模块初始化时启动监听
   */
  async onModuleInit(): Promise<void> {
    // 延迟启动监听，确保其他服务已初始化
    setTimeout(() => {
      this.startMonitoring()
    }, 5000)
  }

  /**
   * 模块销毁时停止监听
   */
  async onModuleDestroy(): Promise<void> {
    this.stopMonitoring()
  }

  /**
   * 启动监听
   */
  startMonitoring(): void {
    if (this.isMonitoring) {
      this.logger.warn('监听已在运行中')
      return
    }

    this.isMonitoring = true
    this.scheduleNextCheck()
  }

  /**
   * 停止监听
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) {
      return
    }

    this.isMonitoring = false
    if (this.monitorTimer) {
      clearTimeout(this.monitorTimer)
      this.monitorTimer = null
    }
    this.logger.log('停止渲染任务状态监听')
  }

  /**
   * 安排下次检查
   */
  private scheduleNextCheck(): void {
    if (!this.isMonitoring) {
      return
    }

    this.monitorTimer = setTimeout(async () => {
      try {
        await this.checkPendingTasks()
      } catch (error) {
        this.logger.error('检查待处理任务失败:', error)
      } finally {
        // 继续安排下次检查
        this.scheduleNextCheck()
      }
    }, this.MONITOR_INTERVAL)
  }

  /**
   * 检查待处理的任务
   */
  private async checkPendingTasks(): Promise<void> {
    try {
      // 获取所有未完成的任务
      const pendingTasks = this.renderTaskService.getPendingTasks()

      if (pendingTasks.length === 0) {
        // this.logger.debug('没有进行中的渲染任务')
        return
      }

      // 过滤掉超时的任务
      const activeTasks = pendingTasks.filter(task => {
        const taskAge = Date.now() - task.created_at
        return taskAge < this.MAX_MONITOR_DURATION
      })

      if (activeTasks.length === 0) {
        // this.logger.debug('没有活跃的渲染任务需要监听')
        return
      }

      // 提取任务编号
      const taskNos = activeTasks.map(task => task.task_no).filter(Boolean)

      if (taskNos.length === 0) {
        // this.logger.warn('没有有效的任务编号')
        return
      }

      // 获取云端进度信息
      const cloudTaskInfos = await this.renderTaskService.getRenderProgress(taskNos)

      if (cloudTaskInfos.length === 0) {
        // this.logger.debug('云端没有返回进度信息')
        return
      }

      // 更新本地任务状态
      await this.renderTaskService.updateTasksProgress(cloudTaskInfos)

      this.logger.debug(
        `当前共有 ${cloudTaskInfos.length} 个任务`
        + cloudTaskInfos.map(info => `\t - NO = ${info.taskNo}, STATUS = ${info.status}, PROGRESS = ${info.progress}`).join('\n')
      )

      // 检查是否有任务完成或失败，记录日志
      for (const info of cloudTaskInfos) {
        if (info.status === RenderTask.RenderTaskStatus.COMPLETED) {
          this.logger.log(`渲染任务完成: ${info.name} (${info.taskNo})`)
        } else if (info.status === RenderTask.RenderTaskStatus.FAILED || info.status === RenderTask.RenderTaskStatus.ASSIGN_FAILED) {
          this.logger.warn(`渲染任务失败: ${info.name} (${info.taskNo}) - ${info.errorMsg || '未知错误'}`)
        }
      }
    } catch (error) {
      this.logger.error('检查待处理任务时发生错误:', error)
    }
  }

  /**
   * 手动触发检查（用于测试或立即更新）
   */
  async triggerCheck(): Promise<void> {
    this.logger.debug('手动触发渲染任务状态检查')
    await this.checkPendingTasks()
  }

  /**
   * 获取监听状态
   */
  isRunning(): boolean {
    return this.isMonitoring
  }

  /**
   * 获取监听统计信息
   */
  getMonitorStats(): {
    isRunning: boolean
    interval: number
    maxDuration: number
    pendingTasksCount: number
  } {
    const pendingTasks = this.renderTaskService.getPendingTasks()

    return {
      isRunning: this.isMonitoring,
      interval: this.MONITOR_INTERVAL,
      maxDuration: this.MAX_MONITOR_DURATION,
      pendingTasksCount: pendingTasks.length
    }
  }
}
