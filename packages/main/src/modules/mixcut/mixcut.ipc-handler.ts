import { Inject, Injectable } from '@nestjs/common'
import { MixcutService } from './mixcut.service.js'
import { BaseIPCHandler } from '@/infra/types/BaseIPCHandler.js'

@Injectable()
export class MixcutIpcHandler extends BaseIPCHandler<'mixcut'> {

  protected readonly platformPrefix = 'mixcut'

  constructor(
    @Inject(MixcutService) private readonly mixcutService: MixcutService
  ) {
    super()
  }

  registerAll() {
    this.registerHandler('generateCombos', data => {
      return this.mixcutService.generateCombos(data)
    })

    this.registerHandler('uploadMixcutResult', data => {
      return this.mixcutService.uploadMixcutResult(data)
    })

    this.registerHandler('cacheMixcutData', data => {
      return this.mixcutService.cacheMixcutData(data)
    })

    this.registerHandler('getMixcutDataFromCache', data => {
      return this.mixcutService.getMixcutDataFromCache(data)
    })

    this.registerHandler('clearMixcuts', data => {
      return this.mixcutService.clearMixcuts(data)
    })
  }
}
