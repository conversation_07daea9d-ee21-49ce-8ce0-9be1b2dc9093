import { Injectable } from '@nestjs/common'
import { MixcutableOverlay } from '@app/shared/types/ipc/mixcut.js'
import { MIXCUT_PIPELINES, MixcutPipelines } from '@app/shared/types/mixcut.js'
import { SimpleOverlaysFilterProcessingPipeline } from '@/modules/mixcut/pipelines/pipelines.js'
import { OverlayType } from '@app/shared/types/overlay.js'
import {
  VideoPositionOffsetProcessor,
  VideoRotationProcessor,
  VideoScaleProcessor,
  VideoSpeedProcessor
} from '../overlay-processors/index.js'
import { MixcutPipelineProcessor } from '@/infra/types/mixcut/mixcut-pipeline.js'

const videoOverlayFilter = (o: MixcutableOverlay) => o.type === OverlayType.VIDEO

const PIPELINE_HANDLER_BY_NAME: Partial<Record<MixcutPipelines, MixcutPipelineProcessor>> = {
  [MIXCUT_PIPELINES.video.rotation]: SimpleOverlaysFilterProcessingPipeline.with(new VideoRotationProcessor(), videoOverlayFilter),
  [MIXCUT_PIPELINES.video.scale]: SimpleOverlaysFilterProcessingPipeline.with(new VideoScaleProcessor(), videoOverlayFilter),
  [MIXCUT_PIPELINES.video.positionOffset]: SimpleOverlaysFilterProcessingPipeline.with(new VideoPositionOffsetProcessor(), videoOverlayFilter),
  [MIXCUT_PIPELINES.video.speed]: SimpleOverlaysFilterProcessingPipeline.with(new VideoSpeedProcessor(), videoOverlayFilter),
}

@Injectable()
export class MixcutPipelineService {

  public async process(
    overlays: MixcutableOverlay[],
    pipelines: Array<{ name: MixcutPipelines, config?: any }>,
  ) {
    for (const pipeline of pipelines) {
      const processor = PIPELINE_HANDLER_BY_NAME[pipeline.name]
      if (!processor) {
        continue
      }

      overlays = await processor.process(overlays)
    }

    return overlays
  }
}
