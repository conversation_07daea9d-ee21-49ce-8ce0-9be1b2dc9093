import crypto from 'crypto'
import zlib from 'zlib'
import { promisify } from 'util'

const gzip = promisify(zlib.gzip)
const gunzip = promisify(zlib.gunzip)

/**
 * 主进程混剪数据加密工具
 * 使用 AES-256-CBC 加密算法对 JSON 数据进行加密和解密
 * 使用 gzip 压缩以减少存储体积
 */
export class MixcutEncryption {

  // 硬编码的加密密钥（根据需求暂时采用明文存储）
  private static readonly ENCRYPTION_KEY = 'mixcut-cache-key-2024-main-process'
  private static readonly ALGORITHM = 'aes-256-cbc'

  /**
   * 生成密钥的 SHA-256 哈希（32字节，适用于 AES-256）
   */
  private static getKeyHash(): Buffer {
    return crypto.createHash('sha256').update(this.ENCRYPTION_KEY).digest()
  }

  /**
   * 压缩并加密 JSON 数据
   * @param data 要加密的数据对象
   * @returns 压缩加密后的 Base64 字符串
   */
  static async compressAndEncrypt(data: Buffer): Promise<Buffer> {
    try {
      const compressed = await gzip(data)

      // 生成随机 IV（16字节）
      const iv = crypto.randomBytes(16)

      // 创建加密器
      const cipher = crypto.createCipheriv(this.ALGORITHM, this.getKeyHash(), iv)

      // 加密压缩数据
      const encrypted = Buffer.concat([
        cipher.update(compressed),
        cipher.final()
      ])

      // 6. 组合 IV + 加密数据
      return Buffer.concat([iv, encrypted])
    } catch (error) {
      console.error('[MixcutEncryption] 压缩加密失败:', error)
      throw new Error('数据压缩加密失败')
    }
  }

  /**
   * 解密并解压数据
   * @param encryptedData 压缩加密的 Base64 字符串
   * @returns 解密解压后的数据对象
   */
  static async decryptAndDecompress<T = any>(encryptedData: Buffer): Promise<T> {
    try {
      // 提取 IV 和加密数据
      const iv = encryptedData.subarray(0, 16)
      const encrypted = encryptedData.subarray(16)

      // 创建解密器
      const decipher = crypto.createDecipheriv(this.ALGORITHM, this.getKeyHash(), iv)

      // 解密数据
      const decrypted = Buffer.concat([
        decipher.update(encrypted),
        decipher.final()
      ])

      const decompressed = await gunzip(decrypted)

      const jsonString = decompressed.toString('utf8')
      return JSON.parse(jsonString)
    } catch (error) {
      console.error('[MixcutEncryption] 解密解压失败:', error)
      throw new Error('数据解密解压失败')
    }
  }
}
