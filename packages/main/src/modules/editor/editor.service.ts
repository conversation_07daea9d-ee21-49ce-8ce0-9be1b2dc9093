import { Inject, Injectable, Logger } from '@nestjs/common'
import { EditorIPCClient } from '@app/shared/types/ipc/editor.js'
import { promises as fs } from 'fs'
import { join } from 'path'
import { tmpdir } from 'os'
import { v4 as uuidv4 } from 'uuid'
import JSZip from 'jszip'
import { getConfiguredFFmpeg } from '@/infra/utils/ffmpeg.js'

import { RequestService } from '@/modules/global/request.service.js'

@Injectable()
export class EditorService implements EditorIPCClient {

  private readonly logger = new Logger(EditorService.name)

  constructor(
    @Inject(RequestService) private requestService: RequestService,
  ) {
  }

  async saveEditorState(props: { editorState: any, scriptId: string }): Promise<void> {
    // TODO: maybe dynamic?
    const CLIENT_ID = 'CLIPNEST'
    const { editorState, scriptId } = props

    let tempJsonPath: string | null = null

    try {
      const uuid = uuidv4()
      const fileName = `${uuid}.json`
      tempJsonPath = join(tmpdir(), fileName)

      const jsonString = JSON.stringify(editorState, null, 2)
      await fs.writeFile(tempJsonPath, jsonString, 'utf8')

      const zip = new JSZip()
      const fileContent = await fs.readFile(tempJsonPath, 'utf8')
      zip.file(fileName, fileContent)

      const zipBuffer = await zip.generateAsync({ type: 'nodebuffer' })
      const uint8Array = new Uint8Array(zipBuffer)
      const byteArray = Array.from(uint8Array)

      await this.requestService.post<boolean>(
        `/app-api/creative/editor/save?scriptId=${scriptId}&clientId=${CLIENT_ID}`,
        byteArray
      )
    } catch (error) {
      this.logger.error('保存编辑器状态失败', error)
      throw new Error('保存编辑器状态失败')
    } finally {
      if (tempJsonPath) {
        try {
          await fs.unlink(tempJsonPath)
        } catch (cleanupError) {
          this.logger.warn('清理临时文件失败:', cleanupError)
        }
      }
    }
  }

  /**
   * 解压二进制数据为 EditorState 对象
   * @returns Promise<any> 解压后的 EditorState 对象
   */
  async loadEditorState(props: { scriptId: string }): Promise<any> {
    const { scriptId } = props

    const data = await this.requestService
      .get<any>(
        `/app-api/creative/editor/info?scriptId=${scriptId}`,
        { ignoreError: true }
      )
      .catch(() => null)

    if (!data) return null

    try {
      let buffer: Buffer

      // 处理不同类型的输入数据
      if (Buffer.isBuffer(data)) {
        buffer = data
      } else if (data instanceof Uint8Array) {
        buffer = Buffer.from(data)
      } else if (Array.isArray(data)) {
        // 处理从云端 API 返回的 number[] 格式
        buffer = Buffer.from(data)
      } else {
        throw new Error('不支持的数据格式')
      }

      const zip = new JSZip()
      const zipContent = await zip.loadAsync(buffer)

      const jsonFiles = Object.keys(zipContent.files).filter(name => name.endsWith('.json'))
      if (jsonFiles.length === 0) {
        throw new Error('压缩包中未找到 JSON 文件')
      }

      const jsonFileName = jsonFiles[0]
      const jsonFile = zipContent.files[jsonFileName]

      const jsonString = await jsonFile.async('text')

      return JSON.parse(jsonString)
    } catch (error) {
      this.logger.error('解压编辑器状态失败:', error)
      throw new Error('解压编辑器状态失败')
    }
  }

  /**
   * 获取音频文件的时长
   * @param audioUrl 音频文件URL
   * @returns Promise<{duration: number, localUrl: string}> 音频时长（秒）和本地文件URL
   */
  async getAudioDuration(audioUrl: string): Promise<number> {
    try {
      return new Promise<number>((resolve, reject) => {
        const ffmpeg = getConfiguredFFmpeg()
        ffmpeg.ffprobe(audioUrl, (err, metadata) => {
          if (err) {
            reject(new Error(`ffprobe 错误: ${err.message}`))
            return
          }

          const duration = metadata.format?.duration
          if (typeof duration !== 'number' || duration <= 0) {
            reject(new Error('无法获取有效的音频时长'))
            return
          }

          resolve(duration)
        })
      })
    } catch (error) {
      this.logger.error('获取音频时长失败:', error)

      return 3
    }
  }
}
