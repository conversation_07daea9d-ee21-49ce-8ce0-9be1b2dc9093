import crypto from 'crypto'
import path from 'path'
import { app } from 'electron'
import { ResourceCacheEntry, ResourceManifest } from '@app/shared/types/resource-cache.types.js'
import { promises as fsPromises } from 'fs'

export function isCacheEntryExpired(entry: { downloadedAt: number }, maxAgeSeconds: number): boolean {
  const now = Date.now()
  return (now - entry.downloadedAt * 1000) > (maxAgeSeconds * 1000)
}

/**
 * 生成 URL 的 SHA1 hash 作为资源的唯一 key
 * @param url 资源 URL
 * @returns SHA1 hash 字符串
 */
export function hashUrl(url: string): string {
  return crypto.createHash('sha1').update(url).digest('hex').slice(0, 12)
}

const MANIFEST_FILE_PATH = path.join(app.getPath('userData'), 'cache', 'resources', 'manifest.json')

// 内存中的 manifest 实例
let currentManifest: ResourceManifest = {}

export namespace ManifestManager {

  /**
   * 加载 manifest.json 文件
   * @returns ResourceManifest 对象，如果文件不存在或解析失败则返回空对象
   */
  export async function loadManifest(): Promise<ResourceManifest> {
    try {
      const data = await fsPromises.readFile(MANIFEST_FILE_PATH, 'utf-8')
      return JSON.parse(data)
    } catch (error: any) {
      if (error.code === 'ENOENT') {
        // 文件不存在，返回空 manifest
        return {}
      }
      console.error('加载 manifest.json 失败:', error)
      return {}
    }
  }

  /**
   * 保存 manifest.json 文件
   * @param manifest 要保存的 ResourceManifest 对象
   */
  export async function saveManifest(manifest: ResourceManifest): Promise<void> {
    try {
      await fsPromises.mkdir(path.dirname(MANIFEST_FILE_PATH), { recursive: true })
      await fsPromises.writeFile(MANIFEST_FILE_PATH, JSON.stringify(manifest, null, 2), 'utf-8')
    } catch (error) {
      console.error('保存 manifest.json 失败:', error)
    }
  }

  /**
   * 初始化 manifest 管理器
   * 加载 manifest 文件到内存
   */
  export async function initManifestManager(): Promise<void> {
    currentManifest = await loadManifest()
  }

  /**
   * 获取指定 key 的缓存条目
   * @param key 资源唯一 key
   * @returns 缓存条目或 undefined
   */
  export function getEntry(key: string): ResourceCacheEntry | undefined {
    return currentManifest[key]
  }

  /**
   * 设置（添加或更新）缓存条目
   * @param entry 要设置的缓存条目
   */
  export async function setEntry(entry: ResourceCacheEntry): Promise<void> {
    currentManifest[entry.key] = entry
    await saveManifest(currentManifest)
  }

  /**
   * 移除指定 key 的缓存条目
   * @param key 要移除的资源唯一 key
   */
  export async function removeEntry(key: string): Promise<void> {
    delete currentManifest[key]
    await saveManifest(currentManifest)
  }

  /**
   * 列出所有缓存条目
   * @returns 缓存条目数组
   */
  export function listAllEntries(): ResourceCacheEntry[] {
    return Object.values(currentManifest)
  }

  /**
   * 批量移除指定 key 的缓存条目
   * @param keys 要移除的资源唯一 key 数组
   */
  export async function batchRemoveEntries(keys: string[]): Promise<void> {
    let hasChanges = false

    for (const key of keys) {
      if (currentManifest[key]) {
        delete currentManifest[key]
        hasChanges = true
      }
    }

    // 只有在有变更时才保存，减少 I/O 操作
    if (hasChanges) {
      await saveManifest(currentManifest)
    }
  }
}
