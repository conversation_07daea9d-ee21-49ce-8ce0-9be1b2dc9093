import { UploadTask } from './types/local-db/entities/upload-task.entity.js'

export const FILE_EXTENSIONS = {
  MEDIA: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', 'm4v', 'mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a'],
  VIDEO: ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', 'm4v'],
  AUDIO: ['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a'],
  IMAGE: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'],
  ALL: ['*'],
} as const

export type FileCategory = keyof typeof FILE_EXTENSIONS
export type FileExtension = typeof FILE_EXTENSIONS[FileCategory][number]

/**
 * 按需生成 filters
 */
export function createFileFilters(categories: FileCategory[]) {
  const nameMap: Record<FileCategory, string> = {
    MEDIA: '媒体文件',
    VIDEO: '视频文件',
    AUDIO: '音频文件',
    IMAGE: '图片文件',
    ALL: '所有文件'
  }

  return categories.map(category => ({
    name: nameMap[category],
    extensions: [...FILE_EXTENSIONS[category]],
  }))
}

export function getFileTypeByExt(ext: string): UploadTask.Type {
  const imageExts = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg']
  const videoExts = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv', '.m4v']
  const audioExts = ['.mp3', '.wav', '.aac', '.flac', '.ogg', '.m4a']

  if (imageExts.includes(ext)) return UploadTask.Type.IMAGE
  if (videoExts.includes(ext)) return UploadTask.Type.VIDEO
  if (audioExts.includes(ext)) return UploadTask.Type.AUDIO
  return UploadTask.Type.OTHER
}
