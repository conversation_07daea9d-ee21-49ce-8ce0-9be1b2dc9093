import { BaseEntity } from '../types/local-db/base.js'
import { PaginatedResult, PaginationParams } from './request.js'

/**
 * 可增删改查的实体 IPC 客户端接口
 */
export interface CrudableIpcClient<
  Entity extends BaseEntity,
  CreateParams,
  ReadParams,
  UpdateParams,
  StatsResult,
> {
  /**
   * 创建实体
   * @param params 创建参数
   * @returns 创建的实体
   */
  create(params: CreateParams): Promise<Entity>;

  /**
   * 获取实体
   * @param props.id 实体ID
   * @param props.includeDeleted 是否包含已删除实体
   * @returns 实体或null
   */
  get(props: { id: number, includeDeleted?: boolean }): Promise<Entity | null>;

  /**
   * 查询实体列表
   * @param props.params 查询参数
   * @param props.pagination 分页参数
   * @returns 分页查询结果
   */
  list(props: { params: ReadParams, pagination?: PaginationParams }): Promise<PaginatedResult<Entity>>;

  /**
   * 更新实体
   * @param props.id 实体ID
   * @param props.params 更新参数
   * @returns 是否更新成功
   */
  update(props: { id: number, params: UpdateParams }): Promise<boolean>;

  /**
   * 删除实体
   * @param id 实体ID
   * @returns 是否删除成功
   */
  delete(id: number): Promise<boolean>;

  /**
   * 永久删除实体
   * @param id 实体ID
   * @returns 是否删除成功
   */
  permanentlyDelete(id: number): Promise<boolean>;

  /**
   * 批量删除实体
   * @param ids 实体ID数组
   * @returns 删除的记录数
   */
  batchDelete(ids: number[]): Promise<number>;

  /**
   * 批量永久删除实体
   * @param ids 实体ID数组
   * @returns 删除的记录数
   */
  batchPermanentlyDelete(ids: number[]): Promise<number>;

  /**
   * 获取实体统计数据
   * @param data 参数对象
   * @returns 统计结果
   */
  getStats(data: { uid: string, teamId?: number | null }): Promise<StatsResult>;
}
