/**
 * 标准 API 响应结构
 */
export interface ApiResponse<T = any> {
  code: number
  data?: T
  msg?: string
  error?: string
}

/**
 * 通用分页查询结果
 */
export interface PaginatedResult<T> {
  list: T[]
  total: number
}

/**
 * 通用排序选项
 */
export interface SortOptions {
  sortBy?: string
  order?: 'asc' | 'desc'
}

/**
 * 通用分页查询参数
 */
export interface PaginationParams extends SortOptions {
  pageNo?: number
  pageSize?: number
}
