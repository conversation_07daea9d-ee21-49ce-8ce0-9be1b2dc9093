
export function sleep(time: number) {
  return new Promise(resolve => setTimeout(resolve, time))
}

export function safeParseJson<T extends Record<string, any> = Record<string, any>>(source?: any): T | null {
  if (!source) return null
  if (typeof source === 'object') return source

  try {
    return JSON.parse(source)
  } catch (e) {
    return null
  }
}

const REPETITION_RATE_TO_SIMILARITY_RATIO = 1e4

export function formatRepetitionRate(rate: number) {
  return (rate / REPETITION_RATE_TO_SIMILARITY_RATIO * 100).toFixed(2) + '%'
}

export function similarityToRepetitionRate(rate: number) {
  return rate * REPETITION_RATE_TO_SIMILARITY_RATIO
}
