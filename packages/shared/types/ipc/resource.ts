import { ResourceCacheEntry, ResourceCacheType } from '../resource-cache.types.js'

/**
 * 资源缓存客户端接口
 */
export interface ResourceIPCClient {
  /**
   * 获取资源的本地路径，如果不存在则下载并缓存
   * @param params.url CDN URL
   * @param params.type 资源类型
   * @param params.version 资源版本
   * @returns 本地文件路径
   */
  fetchOrSaveResource(params: {
    url: string;
    type: ResourceCacheType;
    version?: string;
    customExt?: string
  }): Promise<string>;

  /**
   * 根据 URL 获取缓存条目
   * @param params.url 资源 URL
   * @returns 缓存条目或 undefined
   */
  getResource(params: { url: string }): Promise<ResourceCacheEntry | undefined>;

  /**
   * 获取所有已缓存资源的列表
   * @returns 所有缓存条目的数组
   */
  getAllResources(): Promise<ResourceCacheEntry[]>;
}
