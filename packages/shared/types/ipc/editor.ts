/**
 * 编辑器IPC客户端接口
 */
export interface EditorIPCClient {

  /**
   * 保存编辑器状态数据
   */
  saveEditorState(props: {
    editorState: any,
    scriptId: string,
  }): Promise<void>

  /**
   * 从云端加载编辑器状态数据
   */
  loadEditorState(props: { scriptId: string }): Promise<any>

  /**
   * 获取音频文件的时长
   * @param audioUrl 音频文件URL
   * @returns Promise<{duration: number, localUrl: string}> 音频时长（秒）和本地文件URL
   */
  getAudioDuration(audioUrl: string): Promise<number>
}
