/**
 * 文件过滤器类型定义
 */
export interface FileFilter {
  name: string
  extensions: string[]
}

/**
 * 文件选择配置
 */
export interface FileSelectOptions {
  /**
   * 文件过滤器
   */
  filters?: FileFilter[]
  /**
   * 是否允许多选
   */
  multiple?: boolean
  /**
   * 默认路径
   */
  defaultPath?: string
  /**
   * 标题
   */
  title?: string
}

/**
 * 文件夹选择配置
 */
export interface FolderSelectOptions {
  /**
   * 默认路径
   */
  defaultPath?: string
  /**
   * 标题
   */
  title?: string
  /**
   * 是否允许多选
   */
  multiple?: boolean
}

export interface FileDownloaderIPCClient {
  /**
   * 选择文件夹
   * @param options 选择配置
   * @returns 选择的文件夹路径
   */
  selectFolder: (options?: FolderSelectOptions) => Promise<string[] | null>

  /**
   * 选择文件路径
   * @param options 选择配置
   * @returns 选择的文件路径
   */
  selectFile: (options?: FileSelectOptions) => Promise<string[] | null>

  /**
   * 检查文件是否存在
   * @param filePath 文件路径
   * @returns 是否存在
   */
  fileExists: (filePath: string) => Promise<boolean>

  /**
   * 下载文件到指定路径
   * @param files 文件列表，每个文件包含url和保存路径
   * @param options 请求选项
   * @returns 下载结果列表
   */
  downloadFiles: (files: { url: string; path: string }[]) => Promise<boolean[]>
}
