export enum ResourceCacheType {
  VIDEO  = 'video',
  SOUND = 'sound',
  PASTER = 'paster',
  FONT = 'font',

  /**
   * 无后缀的音乐文件
   */
  SUFFIXLESS_SOUND = 'music',

  /**
   * 混剪数据文件
   */
  MIXCUT = 'mixcut',
}

export enum ResourceTypes {
  paster = 'paster',
  music = 'music',
  voice = 'voice',
  font = 'font',
  fontStyle = 'fontStyle',
  timbre = 'timbre',
  media = 'media',
  directory = 'directory'
}

export interface ResourceCacheEntry {
  // 资源的唯一标识，通常是 URL 的 hash
  key: string;

  // 原始 CDN URL
  url: string;

  // 资源类型
  type: ResourceCacheType;

  // 本地缓存路径
  localPath: string;

  // 资源版本，用于更新判断
  version: string;

  // ETag 值，用于校验资源是否更新
  etag?: string;

  // 上次访问时间戳，用于清理
  lastAccessed: number;

  // 下载完成时间戳
  downloadedAt: number;

  // 文件大小（字节）
  size: number;
}

export interface ResourceManifest {
  // 以 key 为索引的缓存条目集合
  [key: string]: ResourceCacheEntry;
}
