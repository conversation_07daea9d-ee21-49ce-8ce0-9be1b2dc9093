
export const MIXCUT_PIPELINES = {
  video: {
    rotation: 'VIDEO_ROTATION',
    scale: 'VIDEO_SCALE',
    positionOffset: 'VIDEO_POSITION_OFFSET',
    flip: 'VIDEO_FLIP',
    smartClip: 'VIDEO_SMART_CLIP',
    speed: 'VIDEO_SPEED',
    trimEnd: 'VIDEO_TRIM_END',
    masking: 'VIDEO_MASKING',
  },
  narrationText: {
    fontFamily: 'NARRATION_FONT_FAMILY',
    styledText: 'NARRATION_STYLED_TEXT',
  },
  globalText: {
    fontFamily: 'GLOBAL_TEXT_FONT_FAMILY',
    styledText: 'GLOBAL_TEXT_STYLED_TEXT',
  },
  narrationSound: {
    timbre: 'NARRATION_TIMBRE',
  },
}

export type MixcutPipelines =
  | (typeof MIXCUT_PIPELINES.video)[keyof typeof MIXCUT_PIPELINES.video]
  | (typeof MIXCUT_PIPELINES.narrationText)[keyof typeof MIXCUT_PIPELINES.narrationText]
  | (typeof MIXCUT_PIPELINES.globalText)[keyof typeof MIXCUT_PIPELINES.globalText]
  | (typeof MIXCUT_PIPELINES.narrationSound)[keyof typeof MIXCUT_PIPELINES.narrationSound]

// export enum MixcutPipelines {
//   VIDEO_ROTATION = 'VIDEO_ROTATION',
//   VIDEO_SCALE = 'VIDEO_SCALE',
//   VIDEO_POSITION_OFFSET = 'VIDEO_POSITION_OFFSET',
//   VIDEO_FLIP = 'VIDEO_FLIP',
//   VIDEO_SMART_CLIP = 'VIDEO_SMART_CLIP',
//   VIDEO_SPEED = 'VIDEO_SPEED',
//   VIDEO_TRIM_END = 'VIDEO_TRIM_END',
//   VIDEO_MASKING = 'VIDEO_MASKING',
//
//   NARRATION_FONT_FAMILY = 'NARRATION_FONT_FAMILY',
//   NARRATION_STYLED_TEXT = 'NARRATION_STYLED_TEXT',
//   GLOBAL_TEXT_FONT_FAMILY = 'GLOBAL_TEXT_FONT_FAMILY',
//   GLOBAL_TEXT_STYLED_TEXT = 'GLOBAL_TEXT_STYLED_TEXT',
//   NARRATION_TIMBRE = 'NARRATION_TIMBRE',
// }
