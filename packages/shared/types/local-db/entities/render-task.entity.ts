import { BaseEntity, OwnershipFields } from '../base.js'

/**
 * 渲染任务相关类型
 */
export namespace RenderTask {

  /**
   * 渲染任务基础接口
   */
  export interface IRenderTask extends BaseEntity, OwnershipFields {
    id: number

    team_id: number

    // 云端任务编号
    task_no: string

    // 视频名称
    name: string

    // 封面图URL
    cover_url: string

    // 封面图对象ID
    cover_object_id: string

    // 脚本ID
    script_id: number

    // 渲染数据对象ID
    object_id: string

    // 分辨率 (如 "1920x1080")
    resolution: string

    // 帧率
    fps: number

    // 时长(秒)
    duration: number

    // 渲染进度 (0-100)
    progress: number

    // 任务状态
    status: RenderTaskStatus

    // 失败原因
    reason: string

    // 文件大小(字节)
    file_size: number

    // 下载链接
    download_url: string

    // 渲染开始时间戳
    render_start_time: number

    // 渲染结束时间戳
    render_end_time: number
  }

  /**
   * 创建渲染任务参数
   */
  export interface CreateParams extends OwnershipFields
    , Pick<IRenderTask, 'task_no' | 'name' | 'status' | 'progress'>
    , Pick<Partial<IRenderTask>, 'cover_url' | 'cover_object_id' | 'script_id' | 'object_id' | 'resolution' | 'fps' | 'duration'> {
  }

  /**
   * 更新渲染任务参数
   */
  export interface UpdateParams extends Pick<
    Partial<IRenderTask>,
    'cover_url' | 'progress' | 'status' | 'reason' | 'file_size' | 'download_url' | 'render_start_time' | 'render_end_time'
  > {
  }

  /**
   * 查询渲染任务参数
   */
  export interface QueryParams extends Partial<OwnershipFields> {
    status?: RenderTaskStatus | RenderTaskStatus[]
    script_id?: number
    keyword?: string
    start_date?: number
    end_date?: number
  }

  /**
   * 渲染任务统计信息
   */
  export interface TaskStats {
    total_count: number
    pending_count: number
    rendering_count: number
    completed_count: number
    failed_count: number
    cancelled_count: number
  }

  export enum RenderTaskStatus {
    /**
     * 等待渲染
     */
    WAITING = 1,

    /**
     * 任务已分配
     */
    ASSIGNED = 2,

    /**
     * 正在渲染
     */
    RENDERING = 3,

    /**
     * 渲染完成
     */
    COMPLETED = 4,

    /**
     * 上传完成
     */
    UPLOADED = 5,

    /**
     * 渲染失败
     */
    FAILED = 6,

    /**
     * 任务取消
     */
    CANCELED = 7,

    /**
     * 任务分配失败
     */
    ASSIGN_FAILED = 21,

    /**
     * 封面上传完成
     */
    COVER_UPLOADED = 51
  }

  /**
   * 渲染任务进度信息（来自云端API）
   */
  export interface CloudTaskInfo {
    taskNo: string
    name: string
    progress: number
    status: RenderTaskStatus
    errorMsg?: string
    fileSize?: number
    url?: string
  }
}
